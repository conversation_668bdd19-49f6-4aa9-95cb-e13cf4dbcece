// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// NextAuth.js Models
model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Application Models
enum UserRole {
  READER
  AUTHOR
}

enum NovelStatus {
  DRAFT
  PUBLISHED
  COMPLETED
  ARCHIVED
}

enum ChapterStatus {
  DRAFT
  PUBLISHED
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime? @map("email_verified")
  image         String?
  role          UserRole  @default(READER)
  bio           String?   @db.Text
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  // Relations
  accounts        Account[]         @relation
  sessions        Session[]         @relation
  novels          Novel[]           @relation("AuthorNovels")
  library         Library[]         @relation
  readingProgress ReadingProgress[] @relation

  @@map("users")
}

model Novel {
  id          String      @id @default(cuid())
  title       String
  description String?     @db.Text
  synopsis    String?     @db.Text
  coverImage  String?     @map("cover_image")
  status      NovelStatus @default(DRAFT)
  genre       String?
  tags        String[]    @default([])
  authorId    String      @map("author_id")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")
  publishedAt DateTime?   @map("published_at")

  // Relations
  author          User              @relation("AuthorNovels", fields: [authorId], references: [id], onDelete: Cascade)
  chapters        Chapter[]         @relation
  library         Library[]         @relation
  readingProgress ReadingProgress[] @relation

  @@map("novels")
}

model Chapter {
  id        String        @id @default(cuid())
  title     String
  content   String        @db.Text
  order     Int
  status    ChapterStatus @default(DRAFT)
  novelId   String        @map("novel_id")
  createdAt DateTime      @default(now()) @map("created_at")
  updatedAt DateTime      @updatedAt @map("updated_at")

  // Relations
  novel                Novel             @relation(fields: [novelId], references: [id], onDelete: Cascade)
  readingProgress      ReadingProgress[] @relation
  lastChapterProgress  ReadingProgress[] @relation("LastChapterRead")

  @@unique([novelId, order])
  @@map("chapters")
}

model Library {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  novelId   String   @map("novel_id")
  addedAt   DateTime @default(now()) @map("added_at")

  // Relations
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  novel Novel @relation(fields: [novelId], references: [id], onDelete: Cascade)

  @@unique([userId, novelId])
  @@map("library")
}

model ReadingProgress {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  novelId         String   @map("novel_id")
  chapterId       String?  @map("chapter_id")
  lastChapterId   String?  @map("last_chapter_id")
  progress        Float    @default(0) // Percentage (0-100)
  lastReadAt      DateTime @default(now()) @map("last_read_at")
  totalTimeRead   Int      @default(0) @map("total_time_read") // in seconds
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  novel       Novel    @relation(fields: [novelId], references: [id], onDelete: Cascade)
  chapter     Chapter? @relation(fields: [chapterId], references: [id], onDelete: SetNull)
  lastChapter Chapter? @relation("LastChapterRead", fields: [lastChapterId], references: [id], onDelete: SetNull)

  @@unique([userId, novelId])
  @@map("reading_progress")
}