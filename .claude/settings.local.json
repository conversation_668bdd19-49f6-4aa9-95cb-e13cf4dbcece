{"permissions": {"allow": ["Bash(pg_isready:*)", "Bash(supabase projects:*)", "Bash(supabase db:*)", "Bash(supabase status:*)", "Bash(supabase orgs:*)", "Bash(supabase link:*)", "Bash(npx prisma migrate:*)", "Bash(npx prisma:*)", "Bash(grep:*)", "Bash(DATABASE_URL=\"**********************************************************************************/postgres?pgbouncer=true\" DIRECT_URL=\"*****************************************************************************/postgres\" npx prisma db push)", "Bash(find:*)", "Bash(ls:*)", "Bash(rg:*)"], "deny": []}}