#!/bin/bash

# Test New Features Script
# This script helps you test all the newly implemented features

echo "🧪 Black Blogs - New Features Testing Script"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if .env.local exists
echo ""
print_info "Checking environment setup..."

if [ ! -f ".env.local" ]; then
    print_error ".env.local file not found!"
    echo "Please create .env.local file with required environment variables."
    exit 1
fi

# Check for required environment variables
check_env_var() {
    if grep -q "^$1=" .env.local && ! grep -q "^$1=\"\"" .env.local && ! grep -q "^$1=$" .env.local; then
        print_status "$1 is set"
        return 0
    else
        print_error "$1 is missing or empty"
        return 1
    fi
}

echo ""
print_info "Checking required environment variables..."

ENV_CHECK=0
check_env_var "DATABASE_URL" || ENV_CHECK=1
check_env_var "NEXT_PUBLIC_SUPABASE_URL" || ENV_CHECK=1
check_env_var "NEXT_PUBLIC_SUPABASE_ANON_KEY" || ENV_CHECK=1
check_env_var "SUPABASE_SERVICE_ROLE_KEY" || ENV_CHECK=1
check_env_var "NEXTAUTH_SECRET" || ENV_CHECK=1
check_env_var "GOOGLE_CLIENT_ID" || ENV_CHECK=1
check_env_var "GOOGLE_CLIENT_SECRET" || ENV_CHECK=1

if [ $ENV_CHECK -eq 1 ]; then
    echo ""
    print_warning "Some environment variables are missing. Please check NEW_FEATURES_SETUP.md for setup instructions."
    echo ""
fi

# Check if dependencies are installed
echo ""
print_info "Checking dependencies..."

if [ -d "node_modules" ]; then
    print_status "Node modules installed"
else
    print_warning "Node modules not found. Running npm install..."
    npm install
fi

# Check for new dependencies
if npm list react-dropzone > /dev/null 2>&1; then
    print_status "react-dropzone is installed"
else
    print_warning "Installing react-dropzone..."
    npm install react-dropzone
fi

if npm list @radix-ui/react-progress > /dev/null 2>&1; then
    print_status "@radix-ui/react-progress is installed"
else
    print_warning "Installing @radix-ui/react-progress..."
    npm install @radix-ui/react-progress
fi

# Check Prisma setup
echo ""
print_info "Checking database setup..."

if [ -f "prisma/schema.prisma" ]; then
    print_status "Prisma schema found"
    
    # Check if ReadingProgress model exists
    if grep -q "model ReadingProgress" prisma/schema.prisma; then
        print_status "ReadingProgress model found in schema"
    else
        print_error "ReadingProgress model not found in schema"
        print_warning "The schema may not be up to date. Please check the implementation."
    fi
else
    print_error "Prisma schema not found"
fi

# Test API endpoints
echo ""
print_info "Testing API endpoints (requires server to be running)..."

# Check if server is running
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    print_status "Development server is running"
    
    # Test some API endpoints
    echo ""
    print_info "Testing new API endpoints..."
    
    # Test featured novels endpoint
    if curl -s http://localhost:3000/api/novels/featured > /dev/null 2>&1; then
        print_status "Featured novels API is accessible"
    else
        print_warning "Featured novels API not accessible"
    fi
    
    # Test search endpoints
    if curl -s "http://localhost:3000/api/search/novels?q=test" > /dev/null 2>&1; then
        print_status "Novel search API is accessible"
    else
        print_warning "Novel search API not accessible"
    fi
    
else
    print_warning "Development server is not running"
    print_info "Start the server with: npm run dev"
fi

# Check for new files
echo ""
print_info "Checking new feature files..."

NEW_FILES=(
    "src/lib/supabase.ts"
    "src/components/ui/file-upload.tsx"
    "src/components/ui/progress.tsx"
    "src/app/api/upload/cover/route.ts"
    "src/app/api/novels/featured/route.ts"
    "src/app/api/search/novels/route.ts"
    "src/app/api/reading-progress/route.ts"
    "src/app/api/analytics/novels/route.ts"
    "src/app/dashboard/media/page.tsx"
    "src/app/dashboard/analytics/page.tsx"
    "src/app/genres/page.tsx"
    "src/app/history/page.tsx"
)

for file in "${NEW_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_status "$file exists"
    else
        print_error "$file is missing"
    fi
done

# Summary
echo ""
echo "============================================="
print_info "Testing Summary"
echo "============================================="

echo ""
print_info "🎯 Manual Testing Checklist:"
echo ""
echo "1. File Upload System:"
echo "   - Visit /dashboard/novels/[id]/cover"
echo "   - Test drag-and-drop upload"
echo "   - Visit /dashboard/media"
echo ""
echo "2. Chapter Management:"
echo "   - Create chapters in a novel"
echo "   - Test reordering functionality"
echo "   - Test publish/unpublish"
echo ""
echo "3. Search & Discovery:"
echo "   - Visit /browse and test search"
echo "   - Visit /genres"
echo "   - Test /genres/[genre-name]"
echo ""
echo "4. Reading Progress:"
echo "   - Read chapters as a user"
echo "   - Visit /history"
echo ""
echo "5. Analytics:"
echo "   - Visit /dashboard/analytics as author"
echo "   - Test different timeframes"
echo ""

if [ $ENV_CHECK -eq 0 ]; then
    print_status "Environment setup looks good!"
    print_info "Start testing with: npm run dev"
else
    print_warning "Please complete environment setup first."
    print_info "See NEW_FEATURES_SETUP.md for detailed instructions."
fi

echo ""
print_info "🚀 All new features are ready for testing!"
echo "============================================="
