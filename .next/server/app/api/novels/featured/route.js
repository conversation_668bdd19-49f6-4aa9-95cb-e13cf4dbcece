"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/featured/route";
exports.ids = ["app/api/novels/featured/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Ffeatured%2Froute&page=%2Fapi%2Fnovels%2Ffeatured%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Ffeatured%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Ffeatured%2Froute&page=%2Fapi%2Fnovels%2Ffeatured%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Ffeatured%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_featured_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/featured/route.ts */ \"(rsc)/./src/app/api/novels/featured/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/featured/route\",\n        pathname: \"/api/novels/featured\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/featured/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/featured/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_featured_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/featured/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZub3ZlbHMlMkZmZWF0dXJlZCUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGbm92ZWxzJTJGZmVhdHVyZWQlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZub3ZlbHMlMkZmZWF0dXJlZCUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRndlZXJhd2F0JTJGRGVza3RvcCUyRmFkYy1wbGF0Zm9ybSUyRnNlcnZpY2VzJTJGY29udGVudCUyRmJsYWNrLWJsb2clMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGd2VlcmF3YXQlMkZEZXNrdG9wJTJGYWRjLXBsYXRmb3JtJTJGc2VydmljZXMlMkZjb250ZW50JTJGYmxhY2stYmxvZyZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ2M7QUFDcUQ7QUFDbEk7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGdIQUFtQjtBQUMzQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpRUFBaUU7QUFDekU7QUFDQTtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUN1SDs7QUFFdkgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibGFjay1ibG9nLz9jN2I3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi9Vc2Vycy93ZWVyYXdhdC9EZXNrdG9wL2FkYy1wbGF0Zm9ybS9zZXJ2aWNlcy9jb250ZW50L2JsYWNrLWJsb2cvc3JjL2FwcC9hcGkvbm92ZWxzL2ZlYXR1cmVkL3JvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9ub3ZlbHMvZmVhdHVyZWQvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9ub3ZlbHMvZmVhdHVyZWRcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL25vdmVscy9mZWF0dXJlZC9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIi9Vc2Vycy93ZWVyYXdhdC9EZXNrdG9wL2FkYy1wbGF0Zm9ybS9zZXJ2aWNlcy9jb250ZW50L2JsYWNrLWJsb2cvc3JjL2FwcC9hcGkvbm92ZWxzL2ZlYXR1cmVkL3JvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL2FwaS9ub3ZlbHMvZmVhdHVyZWQvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Ffeatured%2Froute&page=%2Fapi%2Fnovels%2Ffeatured%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Ffeatured%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/featured/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/novels/featured/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    const limit = parseInt(searchParams.get(\"limit\") || \"6\");\n    const offset = parseInt(searchParams.get(\"offset\") || \"0\");\n    try {\n        // For now, we'll feature novels based on:\n        // 1. Published status\n        // 2. Has cover image\n        // 3. Has at least one published chapter\n        // 4. Recently updated\n        // 5. Has description/synopsis\n        const featuredNovels = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.novel.findMany({\n            where: {\n                status: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.NovelStatus.PUBLISHED,\n                coverImage: {\n                    not: null\n                },\n                chapters: {\n                    some: {\n                        status: \"PUBLISHED\"\n                    }\n                },\n                OR: [\n                    {\n                        description: {\n                            not: null\n                        }\n                    },\n                    {\n                        synopsis: {\n                            not: null\n                        }\n                    }\n                ]\n            },\n            include: {\n                author: {\n                    select: {\n                        id: true,\n                        name: true,\n                        image: true\n                    }\n                },\n                _count: {\n                    select: {\n                        chapters: {\n                            where: {\n                                status: \"PUBLISHED\"\n                            }\n                        }\n                    }\n                }\n            },\n            orderBy: [\n                // Prioritize novels with more published chapters\n                {\n                    chapters: {\n                        _count: \"desc\"\n                    }\n                },\n                // Then by recent updates\n                {\n                    updatedAt: \"desc\"\n                }\n            ],\n            take: limit,\n            skip: offset\n        });\n        // Get total count for pagination\n        const totalCount = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.novel.count({\n            where: {\n                status: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.NovelStatus.PUBLISHED,\n                coverImage: {\n                    not: null\n                },\n                chapters: {\n                    some: {\n                        status: \"PUBLISHED\"\n                    }\n                },\n                OR: [\n                    {\n                        description: {\n                            not: null\n                        }\n                    },\n                    {\n                        synopsis: {\n                            not: null\n                        }\n                    }\n                ]\n            }\n        });\n        // Transform the data to include additional metadata\n        const transformedNovels = featuredNovels.map((novel)=>({\n                ...novel,\n                publishedChapters: novel._count.chapters,\n                isFeatured: true,\n                featuredReason: \"Popular and well-maintained\"\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            novels: transformedNovels,\n            pagination: {\n                limit,\n                offset,\n                total: totalCount,\n                hasMore: offset + limit < totalCount\n            },\n            metadata: {\n                totalFeatured: totalCount,\n                criteria: [\n                    \"Published status\",\n                    \"Has cover image\",\n                    \"Has published chapters\",\n                    \"Has description or synopsis\",\n                    \"Recently updated\"\n                ]\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching featured novels:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch featured novels\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Admin endpoint to manually feature/unfeature novels\nasync function POST(request) {\n    try {\n        // For now, this is a placeholder for future admin functionality\n        // In a real implementation, you might want to add a \"featured\" field to the Novel model\n        // and allow admins to manually curate featured content\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Manual featuring not implemented yet\"\n        }, {\n            status: 501\n        });\n    } catch (error) {\n        console.error(\"Error featuring novel:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to feature novel\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/featured/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2Ffeatured%2Froute&page=%2Fapi%2Fnovels%2Ffeatured%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2Ffeatured%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();