"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/search/novels/route";
exports.ids = ["app/api/search/novels/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsearch%2Fnovels%2Froute&page=%2Fapi%2Fsearch%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsearch%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsearch%2Fnovels%2Froute&page=%2Fapi%2Fsearch%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsearch%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_search_novels_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/search/novels/route.ts */ \"(rsc)/./src/app/api/search/novels/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/search/novels/route\",\n        pathname: \"/api/search/novels\",\n        filename: \"route\",\n        bundlePath: \"app/api/search/novels/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/search/novels/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_search_novels_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/search/novels/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsearch%2Fnovels%2Froute&page=%2Fapi%2Fsearch%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsearch%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/search/novels/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/search/novels/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    // Search parameters\n    const query = searchParams.get(\"q\") || \"\";\n    const genre = searchParams.get(\"genre\");\n    const author = searchParams.get(\"author\");\n    const status = searchParams.get(\"status\") || _prisma_client__WEBPACK_IMPORTED_MODULE_2__.NovelStatus.PUBLISHED;\n    const tags = searchParams.get(\"tags\")?.split(\",\").filter(Boolean) || [];\n    const minRating = searchParams.get(\"minRating\") ? parseFloat(searchParams.get(\"minRating\")) : undefined;\n    const hasChapters = searchParams.get(\"hasChapters\") === \"true\";\n    const hasCover = searchParams.get(\"hasCover\") === \"true\";\n    // Pagination\n    const page = parseInt(searchParams.get(\"page\") || \"1\");\n    const limit = Math.min(parseInt(searchParams.get(\"limit\") || \"12\"), 50) // Max 50 per page\n    ;\n    const skip = (page - 1) * limit;\n    // Sorting\n    const sortBy = searchParams.get(\"sortBy\") || \"relevance\";\n    const sortOrder = searchParams.get(\"sortOrder\") === \"asc\" ? \"asc\" : \"desc\";\n    try {\n        // Build where clause\n        const where = {\n            status: status\n        };\n        // Text search across title, description, synopsis, and author name\n        if (query.trim()) {\n            where.OR = [\n                {\n                    title: {\n                        contains: query,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    description: {\n                        contains: query,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    synopsis: {\n                        contains: query,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    author: {\n                        name: {\n                            contains: query,\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ];\n        }\n        // Genre filter\n        if (genre) {\n            where.genre = {\n                equals: genre,\n                mode: \"insensitive\"\n            };\n        }\n        // Author filter\n        if (author) {\n            where.author = {\n                name: {\n                    contains: author,\n                    mode: \"insensitive\"\n                }\n            };\n        }\n        // Tags filter (novels that have ALL specified tags)\n        if (tags.length > 0) {\n            where.tags = {\n                hasEvery: tags\n            };\n        }\n        // Has chapters filter\n        if (hasChapters) {\n            where.chapters = {\n                some: {\n                    status: \"PUBLISHED\"\n                }\n            };\n        }\n        // Has cover image filter\n        if (hasCover) {\n            where.coverImage = {\n                not: null\n            };\n        }\n        // Build orderBy clause\n        let orderBy = {};\n        switch(sortBy){\n            case \"title\":\n                orderBy = {\n                    title: sortOrder\n                };\n                break;\n            case \"author\":\n                orderBy = {\n                    author: {\n                        name: sortOrder\n                    }\n                };\n                break;\n            case \"created\":\n                orderBy = {\n                    createdAt: sortOrder\n                };\n                break;\n            case \"updated\":\n                orderBy = {\n                    updatedAt: sortOrder\n                };\n                break;\n            case \"chapters\":\n                orderBy = {\n                    chapters: {\n                        _count: sortOrder\n                    }\n                };\n                break;\n            case \"relevance\":\n            default:\n                // For relevance, prioritize novels with more matches\n                orderBy = [\n                    {\n                        updatedAt: \"desc\"\n                    },\n                    {\n                        chapters: {\n                            _count: \"desc\"\n                        }\n                    },\n                    {\n                        createdAt: \"desc\"\n                    } // Newer novels\n                ];\n                break;\n        }\n        // Execute search\n        const [novels, total] = await Promise.all([\n            _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.novel.findMany({\n                where,\n                include: {\n                    author: {\n                        select: {\n                            id: true,\n                            name: true,\n                            image: true\n                        }\n                    },\n                    _count: {\n                        select: {\n                            chapters: {\n                                where: {\n                                    status: \"PUBLISHED\"\n                                }\n                            }\n                        }\n                    }\n                },\n                orderBy,\n                skip,\n                take: limit\n            }),\n            _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.novel.count({\n                where\n            })\n        ]);\n        // Get available genres for faceted search\n        const availableGenres = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.novel.groupBy({\n            by: [\n                \"genre\"\n            ],\n            where: {\n                status: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.NovelStatus.PUBLISHED,\n                genre: {\n                    not: null\n                }\n            },\n            _count: {\n                genre: true\n            },\n            orderBy: {\n                _count: {\n                    genre: \"desc\"\n                }\n            }\n        });\n        // Get popular tags\n        const popularTags = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.novel.findMany({\n            where: {\n                status: _prisma_client__WEBPACK_IMPORTED_MODULE_2__.NovelStatus.PUBLISHED,\n                tags: {\n                    isEmpty: false\n                }\n            },\n            select: {\n                tags: true\n            }\n        });\n        // Flatten and count tags\n        const tagCounts = {};\n        popularTags.forEach((novel)=>{\n            novel.tags.forEach((tag)=>{\n                tagCounts[tag] = (tagCounts[tag] || 0) + 1;\n            });\n        });\n        const topTags = Object.entries(tagCounts).sort(([, a], [, b])=>b - a).slice(0, 20).map(([tag, count])=>({\n                tag,\n                count\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            novels,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit),\n                hasNext: page * limit < total,\n                hasPrev: page > 1\n            },\n            filters: {\n                query,\n                genre,\n                author,\n                status,\n                tags,\n                minRating,\n                hasChapters,\n                hasCover,\n                sortBy,\n                sortOrder\n            },\n            facets: {\n                genres: availableGenres.map((g)=>({\n                        genre: g.genre,\n                        count: g._count.genre\n                    })),\n                tags: topTags\n            },\n            metadata: {\n                searchTime: Date.now(),\n                totalResults: total\n            }\n        });\n    } catch (error) {\n        console.error(\"Error searching novels:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to search novels\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/search/novels/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsearch%2Fnovels%2Froute&page=%2Fapi%2Fsearch%2Fnovels%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsearch%2Fnovels%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();