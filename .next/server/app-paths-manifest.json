{"/dashboard/page": "app/dashboard/page.js", "/_not-found/page": "app/_not-found/page.js", "/novels/[id]/page": "app/novels/[id]/page.js", "/api/novels/[id]/route": "app/api/novels/[id]/route.js", "/api/library/check/[novelId]/route": "app/api/library/check/[novelId]/route.js", "/browse/page": "app/browse/page.js", "/library/page": "app/library/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js"}