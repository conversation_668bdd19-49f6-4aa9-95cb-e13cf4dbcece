"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/begin-writing-button.tsx":
/*!****************************************************!*\
  !*** ./src/components/ui/begin-writing-button.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BeginWritingButton: function() { return /* binding */ BeginWritingButton; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Loader2_PenTool_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Loader2,PenTool!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-tool.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Loader2_PenTool_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Loader2,PenTool!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Loader2_PenTool_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Loader2,PenTool!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ BeginWritingButton auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction BeginWritingButton() {\n    _s();\n    const { data: session, status, update } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [isUpgrading, setIsUpgrading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleUpgradeToAuthor = async ()=>{\n        try {\n            setIsUpgrading(true);\n            const response = await fetch(\"/api/user/upgrade-to-author\", {\n                method: \"POST\"\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Failed to upgrade to author\");\n            }\n            toast({\n                title: \"Welcome, Author! \\uD83C\\uDF89\",\n                description: result.message\n            });\n            // Update the session to reflect the new role\n            await update();\n            // Close dialog and redirect to dashboard\n            setIsOpen(false);\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Error upgrading to author:\", error);\n            toast({\n                title: \"Upgrade Failed\",\n                description: error instanceof Error ? error.message : \"Failed to upgrade to author\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsUpgrading(false);\n        }\n    };\n    // Loading state\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n            variant: \"outline\",\n            size: \"lg\",\n            className: \"text-lg px-8\",\n            disabled: true,\n            children: \"Begin Writing\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    // Not authenticated - redirect to sign in\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n            asChild: true,\n            variant: \"outline\",\n            size: \"lg\",\n            className: \"text-lg px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                href: \"/auth/signin\",\n                children: \"Begin Writing\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    }\n    // Already an author - go to dashboard\n    if (session.user.role === \"AUTHOR\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n            asChild: true,\n            variant: \"outline\",\n            size: \"lg\",\n            className: \"text-lg px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                href: \"/dashboard\",\n                children: \"Go to Dashboard\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this);\n    }\n    // Reader who wants to become an author\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.Dialog, {\n        open: isOpen,\n        onOpenChange: setIsOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"outline\",\n                    size: \"lg\",\n                    className: \"text-lg px-8\",\n                    children: \"Begin Writing\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogContent, {\n                className: \"sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Loader2_PenTool_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Become an Author\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_5__.DialogDescription, {\n                                children: \"Ready to share your stories with the world? Here's how to get started as an author on Black Blogs.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-4\",\n                                        children: \"Ready to share your stories with the world? Upgrade your account to start writing and publishing novels on Black Blogs.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted/50 rounded-lg p-4 space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-foreground\",\n                                                children: \"What you'll get as an author:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1 text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-1.5 h-1.5 rounded-full bg-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Create and publish unlimited novels\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-1.5 h-1.5 rounded-full bg-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Upload custom cover images\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-1.5 h-1.5 rounded-full bg-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Track reader analytics and engagement\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-1.5 h-1.5 rounded-full bg-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Manage chapters and publishing schedule\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-1.5 h-1.5 rounded-full bg-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Connect with your readers\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleUpgradeToAuthor,\n                                        disabled: isUpgrading,\n                                        className: \"w-full\",\n                                        children: isUpgrading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Loader2_PenTool_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Upgrading...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Loader2_PenTool_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Become an Author\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        asChild: true,\n                                        variant: \"outline\",\n                                        className: \"w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            href: \"/browse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Loader2_PenTool_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Continue Reading\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/components/ui/begin-writing-button.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(BeginWritingButton, \"qReWEADqDXW4+OZ1TpIPS8/la2Q=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = BeginWritingButton;\nvar _c;\n$RefreshReg$(_c, \"BeginWritingButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/begin-writing-button.tsx\n"));

/***/ })

});