import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// File upload utilities
export async function uploadCoverImage(file: File, novelId: string): Promise<string> {
  const fileExt = file.name.split('.').pop()?.toLowerCase()
  const fileName = `${novelId}.${fileExt}`
  
  // Validate file type
  const allowedTypes = ['jpg', 'jpeg', 'png', 'webp']
  if (!fileExt || !allowedTypes.includes(fileExt)) {
    throw new Error('Invalid file type. Only JPG, PNG, and WebP files are allowed.')
  }
  
  // Validate file size (max 5MB)
  const maxSize = 5 * 1024 * 1024 // 5MB in bytes
  if (file.size > maxSize) {
    throw new Error('File size too large. Maximum size is 5MB.')
  }
  
  const { data, error } = await supabase.storage
    .from('covers')
    .upload(fileName, file, { 
      upsert: true,
      contentType: file.type 
    })
    
  if (error) {
    throw new Error(`Upload failed: ${error.message}`)
  }
  
  // Get public URL
  const { data: { publicUrl } } = supabase.storage
    .from('covers')
    .getPublicUrl(fileName)
    
  return publicUrl
}

export async function deleteCoverImage(fileName: string): Promise<void> {
  const { error } = await supabase.storage
    .from('covers')
    .remove([fileName])
    
  if (error) {
    throw new Error(`Delete failed: ${error.message}`)
  }
}

export function getCoverImageUrl(fileName: string): string {
  const { data: { publicUrl } } = supabase.storage
    .from('covers')
    .getPublicUrl(fileName)
    
  return publicUrl
}
