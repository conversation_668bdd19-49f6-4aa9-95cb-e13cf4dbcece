import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"

interface RouteParams {
  params: {
    id: string
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: RouteParams
) {
  const { id } = params

  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get chapter and verify ownership
    const chapter = await prisma.chapter.findUnique({
      where: { id },
      include: {
        novel: {
          select: {
            id: true,
            authorId: true,
          },
        },
      },
    })

    if (!chapter) {
      return NextResponse.json({ error: "Chapter not found" }, { status: 404 })
    }

    if (chapter.novel.authorId !== session.user.id) {
      return NextResponse.json(
        { error: "You don't have permission to edit this chapter" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { newOrder } = body

    // Validate new order
    if (typeof newOrder !== "number" || newOrder < 1) {
      return NextResponse.json(
        { error: "Invalid order. Must be a positive number." },
        { status: 400 }
      )
    }

    const currentOrder = chapter.order
    const novelId = chapter.novelId

    // If order hasn't changed, return early
    if (currentOrder === newOrder) {
      return NextResponse.json({
        success: true,
        data: chapter,
        message: "Chapter order unchanged",
      })
    }

    // Use a transaction to ensure consistency
    await prisma.$transaction(async (tx) => {
      if (newOrder > currentOrder) {
        // Moving down: shift chapters between current and new position up
        await tx.chapter.updateMany({
          where: {
            novelId,
            order: {
              gt: currentOrder,
              lte: newOrder,
            },
          },
          data: {
            order: {
              decrement: 1,
            },
          },
        })
      } else {
        // Moving up: shift chapters between new and current position down
        await tx.chapter.updateMany({
          where: {
            novelId,
            order: {
              gte: newOrder,
              lt: currentOrder,
            },
          },
          data: {
            order: {
              increment: 1,
            },
          },
        })
      }

      // Update the target chapter's order
      await tx.chapter.update({
        where: { id },
        data: { order: newOrder },
      })
    })

    // Fetch updated chapter
    const updatedChapter = await prisma.chapter.findUnique({
      where: { id },
      include: {
        novel: {
          select: {
            id: true,
            title: true,
            authorId: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: updatedChapter,
      message: "Chapter order updated successfully",
    })
  } catch (error) {
    console.error("Error reordering chapter:", error)
    return NextResponse.json(
      { error: "Failed to reorder chapter" },
      { status: 500 }
    )
  }
}
