import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { deleteCoverImage } from "@/lib/supabase"

interface RouteParams {
  params: {
    filename: string
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json(
        { error: "Unauthorized. Only authors can delete cover images." },
        { status: 401 }
      )
    }

    const { filename } = params

    if (!filename) {
      return NextResponse.json(
        { error: "Filename is required" },
        { status: 400 }
      )
    }

    // Extract novel ID from filename (assuming format: novelId.ext)
    const novelId = filename.split('.')[0]

    // Verify novel ownership
    const novel = await prisma.novel.findFirst({
      where: {
        id: novelId,
        authorId: session.user.id,
      },
    })

    if (!novel) {
      return NextResponse.json(
        { error: "Novel not found or you don't have permission to edit it" },
        { status: 404 }
      )
    }

    // Delete file from Supabase Storage
    await deleteCoverImage(filename)

    // Update novel to remove cover image URL
    const updatedNovel = await prisma.novel.update({
      where: { id: novelId },
      data: { coverImage: null },
      include: {
        author: { select: { id: true, name: true, image: true } },
        _count: { select: { chapters: true } },
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        novel: updatedNovel,
      },
      message: "Cover image deleted successfully",
    })
  } catch (error) {
    console.error("Cover delete error:", error)
    
    const errorMessage = error instanceof Error ? error.message : "Failed to delete cover image"
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}
