import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { uploadCoverImage } from "@/lib/supabase"

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json(
        { error: "Unauthorized. Only authors can upload cover images." },
        { status: 401 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get("file") as File
    const novelId = formData.get("novelId") as string

    // Validate inputs
    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      )
    }

    if (!novelId) {
      return NextResponse.json(
        { error: "Novel ID is required" },
        { status: 400 }
      )
    }

    // Verify novel ownership
    const novel = await prisma.novel.findFirst({
      where: {
        id: novelId,
        authorId: session.user.id,
      },
    })

    if (!novel) {
      return NextResponse.json(
        { error: "Novel not found or you don't have permission to edit it" },
        { status: 404 }
      )
    }

    // Upload file to Supabase Storage
    const publicUrl = await uploadCoverImage(file, novelId)

    // Update novel with cover image URL
    const updatedNovel = await prisma.novel.update({
      where: { id: novelId },
      data: { coverImage: publicUrl },
      include: {
        author: { select: { id: true, name: true, image: true } },
        _count: { select: { chapters: true } },
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        novel: updatedNovel,
        coverUrl: publicUrl,
      },
      message: "Cover image uploaded successfully",
    })
  } catch (error) {
    console.error("Cover upload error:", error)
    
    const errorMessage = error instanceof Error ? error.message : "Failed to upload cover image"
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    )
  }
}
