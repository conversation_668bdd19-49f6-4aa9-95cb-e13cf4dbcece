import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Check if user is already an author
    if (session.user.role === "AUTHOR") {
      return NextResponse.json({
        success: true,
        message: "You are already an author",
        user: session.user,
      })
    }

    // Update user role to AUTHOR
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: { role: "AUTHOR" },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        role: true,
        bio: true,
        createdAt: true,
      },
    })

    return NextResponse.json({
      success: true,
      message: "Congratulations! You are now an author. You can start creating novels.",
      user: updatedUser,
    })
  } catch (error) {
    console.error("Error upgrading user to author:", error)
    return NextResponse.json(
      { error: "Failed to upgrade to author" },
      { status: 500 }
    )
  }
}
