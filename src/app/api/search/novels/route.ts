import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/db"
import { NovelStatus } from "@prisma/client"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  
  // Search parameters
  const query = searchParams.get("q") || ""
  const genre = searchParams.get("genre")
  const author = searchParams.get("author")
  const status = searchParams.get("status") || NovelStatus.PUBLISHED
  const tags = searchParams.get("tags")?.split(",").filter(Boolean) || []
  const minRating = searchParams.get("minRating") ? parseFloat(searchParams.get("minRating")!) : undefined
  const hasChapters = searchParams.get("hasChapters") === "true"
  const hasCover = searchParams.get("hasCover") === "true"
  
  // Pagination
  const page = parseInt(searchParams.get("page") || "1")
  const limit = Math.min(parseInt(searchParams.get("limit") || "12"), 50) // Max 50 per page
  const skip = (page - 1) * limit
  
  // Sorting
  const sortBy = searchParams.get("sortBy") || "relevance"
  const sortOrder = searchParams.get("sortOrder") === "asc" ? "asc" : "desc"

  try {
    // Build where clause
    const where: any = {
      status: status as NovelStatus,
    }

    // Text search across title, description, synopsis, and author name
    if (query.trim()) {
      where.OR = [
        { title: { contains: query, mode: "insensitive" } },
        { description: { contains: query, mode: "insensitive" } },
        { synopsis: { contains: query, mode: "insensitive" } },
        { author: { name: { contains: query, mode: "insensitive" } } },
      ]
    }

    // Genre filter
    if (genre) {
      where.genre = { equals: genre, mode: "insensitive" }
    }

    // Author filter
    if (author) {
      where.author = {
        name: { contains: author, mode: "insensitive" }
      }
    }

    // Tags filter (novels that have ALL specified tags)
    if (tags.length > 0) {
      where.tags = {
        hasEvery: tags
      }
    }

    // Has chapters filter
    if (hasChapters) {
      where.chapters = {
        some: {
          status: "PUBLISHED"
        }
      }
    }

    // Has cover image filter
    if (hasCover) {
      where.coverImage = {
        not: null
      }
    }

    // Build orderBy clause
    let orderBy: any = {}
    
    switch (sortBy) {
      case "title":
        orderBy = { title: sortOrder }
        break
      case "author":
        orderBy = { author: { name: sortOrder } }
        break
      case "created":
        orderBy = { createdAt: sortOrder }
        break
      case "updated":
        orderBy = { updatedAt: sortOrder }
        break
      case "chapters":
        orderBy = { chapters: { _count: sortOrder } }
        break
      case "relevance":
      default:
        // For relevance, prioritize novels with more matches
        orderBy = [
          { updatedAt: "desc" }, // Recent updates first
          { chapters: { _count: "desc" } }, // More chapters
          { createdAt: "desc" } // Newer novels
        ]
        break
    }

    // Execute search
    const [novels, total] = await Promise.all([
      prisma.novel.findMany({
        where,
        include: {
          author: { 
            select: { 
              id: true, 
              name: true, 
              image: true 
            } 
          },
          _count: { 
            select: { 
              chapters: {
                where: {
                  status: "PUBLISHED"
                }
              }
            } 
          },
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.novel.count({ where }),
    ])

    // Get available genres for faceted search
    const availableGenres = await prisma.novel.groupBy({
      by: ['genre'],
      where: {
        status: NovelStatus.PUBLISHED,
        genre: {
          not: null
        }
      },
      _count: {
        genre: true
      },
      orderBy: {
        _count: {
          genre: 'desc'
        }
      }
    })

    // Get popular tags
    const popularTags = await prisma.novel.findMany({
      where: {
        status: NovelStatus.PUBLISHED,
        tags: {
          isEmpty: false
        }
      },
      select: {
        tags: true
      }
    })

    // Flatten and count tags
    const tagCounts: Record<string, number> = {}
    popularTags.forEach(novel => {
      novel.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1
      })
    })

    const topTags = Object.entries(tagCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 20)
      .map(([tag, count]) => ({ tag, count }))

    return NextResponse.json({
      novels,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
      filters: {
        query,
        genre,
        author,
        status,
        tags,
        minRating,
        hasChapters,
        hasCover,
        sortBy,
        sortOrder,
      },
      facets: {
        genres: availableGenres.map(g => ({
          genre: g.genre,
          count: g._count.genre
        })),
        tags: topTags,
      },
      metadata: {
        searchTime: Date.now(),
        totalResults: total,
      }
    })
  } catch (error) {
    console.error("Error searching novels:", error)
    return NextResponse.json(
      { error: "Failed to search novels" },
      { status: 500 }
    )
  }
}
