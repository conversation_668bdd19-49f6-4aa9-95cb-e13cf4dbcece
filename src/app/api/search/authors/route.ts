import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/db"
import { NovelStatus } from "@prisma/client"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  
  // Search parameters
  const query = searchParams.get("q") || ""
  const hasNovels = searchParams.get("hasNovels") === "true"
  const minNovels = searchParams.get("minNovels") ? parseInt(searchParams.get("minNovels")!) : undefined
  
  // Pagination
  const page = parseInt(searchParams.get("page") || "1")
  const limit = Math.min(parseInt(searchParams.get("limit") || "12"), 50) // Max 50 per page
  const skip = (page - 1) * limit
  
  // Sorting
  const sortBy = searchParams.get("sortBy") || "relevance"
  const sortOrder = searchParams.get("sortOrder") === "asc" ? "asc" : "desc"

  try {
    // Build where clause
    const where: any = {
      role: "AUTHOR", // Only show users with AUTHOR role
    }

    // Text search across name and bio
    if (query.trim()) {
      where.OR = [
        { name: { contains: query, mode: "insensitive" } },
        { bio: { contains: query, mode: "insensitive" } },
      ]
    }

    // Has published novels filter
    if (hasNovels) {
      where.novels = {
        some: {
          status: NovelStatus.PUBLISHED
        }
      }
    }

    // Minimum novels filter
    if (minNovels !== undefined) {
      where.novels = {
        ...where.novels,
        some: {
          status: NovelStatus.PUBLISHED
        }
      }
    }

    // Build orderBy clause
    let orderBy: any = {}
    
    switch (sortBy) {
      case "name":
        orderBy = { name: sortOrder }
        break
      case "joined":
        orderBy = { createdAt: sortOrder }
        break
      case "novels":
        orderBy = { novels: { _count: sortOrder } }
        break
      case "relevance":
      default:
        // For relevance, prioritize authors with more published novels
        orderBy = [
          { novels: { _count: "desc" } }, // More novels
          { createdAt: "desc" } // Newer authors
        ]
        break
    }

    // Execute search
    const [authors, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          name: true,
          image: true,
          bio: true,
          createdAt: true,
          _count: {
            select: {
              novels: {
                where: {
                  status: NovelStatus.PUBLISHED
                }
              }
            }
          },
          novels: {
            where: {
              status: NovelStatus.PUBLISHED
            },
            select: {
              id: true,
              title: true,
              coverImage: true,
              genre: true,
              createdAt: true,
              _count: {
                select: {
                  chapters: {
                    where: {
                      status: "PUBLISHED"
                    }
                  }
                }
              }
            },
            orderBy: {
              updatedAt: "desc"
            },
            take: 3 // Show up to 3 recent novels per author
          }
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.user.count({ where }),
    ])

    // Filter authors with minimum novels if specified
    let filteredAuthors = authors
    if (minNovels !== undefined) {
      filteredAuthors = authors.filter(author => author._count.novels >= minNovels)
    }

    // Transform data to include additional metadata
    const transformedAuthors = filteredAuthors.map(author => ({
      id: author.id,
      name: author.name,
      image: author.image,
      bio: author.bio,
      memberSince: author.createdAt,
      stats: {
        totalNovels: author._count.novels,
        totalChapters: author.novels.reduce((sum, novel) => sum + novel._count.chapters, 0),
      },
      recentNovels: author.novels,
      genres: [...new Set(author.novels.map(novel => novel.genre).filter(Boolean))],
    }))

    // Get genre distribution for faceted search
    const genreStats = await prisma.novel.groupBy({
      by: ['genre'],
      where: {
        status: NovelStatus.PUBLISHED,
        genre: {
          not: null
        },
        author: {
          role: "AUTHOR"
        }
      },
      _count: {
        authorId: true
      },
      orderBy: {
        _count: {
          authorId: 'desc'
        }
      }
    })

    return NextResponse.json({
      authors: transformedAuthors,
      pagination: {
        page,
        limit,
        total: filteredAuthors.length,
        pages: Math.ceil(filteredAuthors.length / limit),
        hasNext: page * limit < filteredAuthors.length,
        hasPrev: page > 1,
      },
      filters: {
        query,
        hasNovels,
        minNovels,
        sortBy,
        sortOrder,
      },
      facets: {
        genres: genreStats.map(g => ({
          genre: g.genre,
          authorCount: g._count.authorId
        })),
      },
      metadata: {
        searchTime: Date.now(),
        totalResults: filteredAuthors.length,
        totalAuthors: <AUTHORS>
      }
    })
  } catch (error) {
    console.error("Error searching authors:", error)
    return NextResponse.json(
      { error: "Failed to search authors" },
      { status: 500 }
    )
  }
}
