import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { ChapterStatus, NovelStatus } from "@prisma/client"

interface RouteParams {
  params: {
    novelId: string
  }
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  const { novelId } = params
  const { searchParams } = new URL(request.url)
  const includeUnpublished = searchParams.get("includeUnpublished") === "true"

  try {
    // Get novel and check permissions
    const novel = await prisma.novel.findUnique({
      where: { id: novelId },
      select: {
        id: true,
        title: true,
        authorId: true,
        status: true,
      },
    })

    if (!novel) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    // Check authentication for unpublished content
    const session = await getServerSession(authOptions)
    const isAuthor = session?.user?.id === novel.authorId

    // Determine chapter status filter
    let chapterStatusFilter: ChapterStatus[] = [ChapterStatus.PUBLISHED]
    
    if (includeUnpublished && isAuthor) {
      chapterStatusFilter = [ChapterStatus.PUBLISHED, ChapterStatus.DRAFT]
    }

    // Only allow access to unpublished novels for authors
    if (novel.status !== NovelStatus.PUBLISHED && !isAuthor) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    // Fetch chapters
    const chapters = await prisma.chapter.findMany({
      where: {
        novelId,
        status: {
          in: chapterStatusFilter,
        },
      },
      select: {
        id: true,
        title: true,
        order: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        // Only include content for authors
        ...(isAuthor && { content: true }),
      },
      orderBy: {
        order: "asc",
      },
    })

    return NextResponse.json({
      novel: {
        id: novel.id,
        title: novel.title,
        status: novel.status,
      },
      chapters,
      totalChapters: chapters.length,
    })
  } catch (error) {
    console.error("Error fetching chapters:", error)
    return NextResponse.json(
      { error: "Failed to fetch chapters" },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  const { novelId } = params

  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Verify novel ownership
    const novel = await prisma.novel.findFirst({
      where: {
        id: novelId,
        authorId: session.user.id,
      },
    })

    if (!novel) {
      return NextResponse.json(
        { error: "Novel not found or you don't have permission to edit it" },
        { status: 404 }
      )
    }

    const body = await request.json()
    const { title, content } = body

    // Validate required fields
    if (!title || title.trim().length === 0) {
      return NextResponse.json(
        { error: "Title is required" },
        { status: 400 }
      )
    }

    if (!content || content.trim().length === 0) {
      return NextResponse.json(
        { error: "Content is required" },
        { status: 400 }
      )
    }

    // Get the next order number
    const lastChapter = await prisma.chapter.findFirst({
      where: { novelId },
      orderBy: { order: "desc" },
      select: { order: true },
    })

    const nextOrder = (lastChapter?.order || 0) + 1

    // Create chapter
    const chapter = await prisma.chapter.create({
      data: {
        title: title.trim(),
        content: content.trim(),
        order: nextOrder,
        novelId,
        status: ChapterStatus.DRAFT,
      },
      include: {
        novel: {
          select: {
            id: true,
            title: true,
            authorId: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: chapter,
      message: "Chapter created successfully",
    })
  } catch (error) {
    console.error("Error creating chapter:", error)
    return NextResponse.json(
      { error: "Failed to create chapter" },
      { status: 500 }
    )
  }
}
