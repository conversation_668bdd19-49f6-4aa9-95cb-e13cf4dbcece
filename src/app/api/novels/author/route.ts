import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { NovelStatus } from "@prisma/client"

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "12")
    const status = searchParams.get("status") // Optional status filter
    const search = searchParams.get("search") // Optional search term

    const skip = (page - 1) * limit

    // Build where clause for author's novels
    const where: any = {
      authorId: session.user.id,
    }

    // Add status filter if provided
    if (status && Object.values(NovelStatus).includes(status as NovelStatus)) {
      where.status = status as NovelStatus
    }

    // Add search filter if provided
    if (search && search.trim()) {
      where.OR = [
        { title: { contains: search.trim(), mode: "insensitive" } },
        { description: { contains: search.trim(), mode: "insensitive" } },
        { synopsis: { contains: search.trim(), mode: "insensitive" } },
      ]
    }

    // Fetch author's novels with pagination
    const [novels, total] = await Promise.all([
      prisma.novel.findMany({
        where,
        include: {
          author: { 
            select: { 
              id: true, 
              name: true, 
              image: true 
            } 
          },
          _count: { 
            select: { 
              chapters: true,
              library: true, // Number of users who added to library
            } 
          },
        },
        orderBy: { updatedAt: "desc" }, // Most recently updated first
        skip,
        take: limit,
      }),
      prisma.novel.count({ where }),
    ])

    // Calculate additional statistics
    const stats = {
      total: total,
      published: await prisma.novel.count({
        where: { authorId: session.user.id, status: NovelStatus.PUBLISHED }
      }),
      draft: await prisma.novel.count({
        where: { authorId: session.user.id, status: NovelStatus.DRAFT }
      }),
      totalChapters: await prisma.chapter.count({
        where: { novel: { authorId: session.user.id } }
      }),
      totalFollowers: await prisma.library.count({
        where: { novel: { authorId: session.user.id } }
      }),
    }

    return NextResponse.json({
      novels,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
      stats,
    })
  } catch (error) {
    console.error("Error fetching author novels:", error)
    return NextResponse.json(
      { error: "Failed to fetch author novels" },
      { status: 500 }
    )
  }
}
