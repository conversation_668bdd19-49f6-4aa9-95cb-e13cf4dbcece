import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get("limit") || "10")
    const offset = parseInt(searchParams.get("offset") || "0")

    // Get user's reading progress
    const readingProgress = await prisma.readingProgress.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        novel: {
          select: {
            id: true,
            title: true,
            coverImage: true,
            status: true,
            author: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
            _count: {
              select: {
                chapters: {
                  where: {
                    status: "PUBLISHED",
                  },
                },
              },
            },
          },
        },
        chapter: {
          select: {
            id: true,
            title: true,
            order: true,
          },
        },
        lastChapter: {
          select: {
            id: true,
            title: true,
            order: true,
          },
        },
      },
      orderBy: {
        lastReadAt: "desc",
      },
      take: limit,
      skip: offset,
    })

    // Get total count
    const totalCount = await prisma.readingProgress.count({
      where: {
        userId: session.user.id,
      },
    })

    return NextResponse.json({
      readingProgress,
      pagination: {
        limit,
        offset,
        total: totalCount,
        hasMore: offset + limit < totalCount,
      },
    })
  } catch (error) {
    console.error("Error fetching reading progress:", error)
    return NextResponse.json(
      { error: "Failed to fetch reading progress" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { novelId, chapterId, progress, timeSpent } = body

    // Validate inputs
    if (!novelId) {
      return NextResponse.json(
        { error: "Novel ID is required" },
        { status: 400 }
      )
    }

    if (progress !== undefined && (progress < 0 || progress > 100)) {
      return NextResponse.json(
        { error: "Progress must be between 0 and 100" },
        { status: 400 }
      )
    }

    // Verify novel exists and is published
    const novel = await prisma.novel.findFirst({
      where: {
        id: novelId,
        status: "PUBLISHED",
      },
    })

    if (!novel) {
      return NextResponse.json(
        { error: "Novel not found or not published" },
        { status: 404 }
      )
    }

    // Verify chapter exists if provided
    let chapter = null
    if (chapterId) {
      chapter = await prisma.chapter.findFirst({
        where: {
          id: chapterId,
          novelId,
          status: "PUBLISHED",
        },
      })

      if (!chapter) {
        return NextResponse.json(
          { error: "Chapter not found or not published" },
          { status: 404 }
        )
      }
    }

    // Update or create reading progress
    const readingProgressData = {
      userId: session.user.id,
      novelId,
      ...(chapterId && { chapterId }),
      ...(chapterId && { lastChapterId: chapterId }),
      ...(progress !== undefined && { progress }),
      lastReadAt: new Date(),
      ...(timeSpent && { 
        totalTimeRead: {
          increment: timeSpent
        }
      }),
    }

    const updatedProgress = await prisma.readingProgress.upsert({
      where: {
        userId_novelId: {
          userId: session.user.id,
          novelId,
        },
      },
      update: readingProgressData,
      create: {
        ...readingProgressData,
        totalTimeRead: timeSpent || 0,
      },
      include: {
        novel: {
          select: {
            id: true,
            title: true,
            coverImage: true,
            author: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        chapter: {
          select: {
            id: true,
            title: true,
            order: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: updatedProgress,
      message: "Reading progress updated successfully",
    })
  } catch (error) {
    console.error("Error updating reading progress:", error)
    return NextResponse.json(
      { error: "Failed to update reading progress" },
      { status: 500 }
    )
  }
}
