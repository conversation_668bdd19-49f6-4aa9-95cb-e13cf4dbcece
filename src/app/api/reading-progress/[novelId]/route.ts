import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"

interface RouteParams {
  params: {
    novelId: string
  }
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  const { novelId } = params

  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get reading progress for specific novel
    const readingProgress = await prisma.readingProgress.findUnique({
      where: {
        userId_novelId: {
          userId: session.user.id,
          novelId,
        },
      },
      include: {
        novel: {
          select: {
            id: true,
            title: true,
            coverImage: true,
            status: true,
            author: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
            _count: {
              select: {
                chapters: {
                  where: {
                    status: "PUBLISHED",
                  },
                },
              },
            },
          },
        },
        chapter: {
          select: {
            id: true,
            title: true,
            order: true,
            status: true,
          },
        },
        lastChapter: {
          select: {
            id: true,
            title: true,
            order: true,
            status: true,
          },
        },
      },
    })

    // If no progress exists, return default values
    if (!readingProgress) {
      const novel = await prisma.novel.findUnique({
        where: { id: novelId },
        select: {
          id: true,
          title: true,
          coverImage: true,
          status: true,
          author: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          _count: {
            select: {
              chapters: {
                where: {
                  status: "PUBLISHED",
                },
              },
            },
          },
        },
      })

      if (!novel) {
        return NextResponse.json(
          { error: "Novel not found" },
          { status: 404 }
        )
      }

      return NextResponse.json({
        readingProgress: {
          id: null,
          userId: session.user.id,
          novelId,
          chapterId: null,
          lastChapterId: null,
          progress: 0,
          lastReadAt: null,
          totalTimeRead: 0,
          novel,
          chapter: null,
          lastChapter: null,
        },
      })
    }

    // Calculate additional statistics
    const totalChapters = readingProgress.novel._count.chapters
    const currentChapterOrder = readingProgress.lastChapter?.order || 0
    const calculatedProgress = totalChapters > 0 
      ? Math.round((currentChapterOrder / totalChapters) * 100)
      : 0

    // Get next chapter to read
    const nextChapter = await prisma.chapter.findFirst({
      where: {
        novelId,
        status: "PUBLISHED",
        order: {
          gt: currentChapterOrder,
        },
      },
      select: {
        id: true,
        title: true,
        order: true,
      },
      orderBy: {
        order: "asc",
      },
    })

    return NextResponse.json({
      readingProgress: {
        ...readingProgress,
        calculatedProgress,
        nextChapter,
        stats: {
          totalChapters,
          chaptersRead: currentChapterOrder,
          timeReadFormatted: formatReadingTime(readingProgress.totalTimeRead),
        },
      },
    })
  } catch (error) {
    console.error("Error fetching novel reading progress:", error)
    return NextResponse.json(
      { error: "Failed to fetch reading progress" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  const { novelId } = params

  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Delete reading progress
    const deletedProgress = await prisma.readingProgress.delete({
      where: {
        userId_novelId: {
          userId: session.user.id,
          novelId,
        },
      },
    })

    return NextResponse.json({
      success: true,
      message: "Reading progress deleted successfully",
    })
  } catch (error) {
    if (error.code === "P2025") {
      return NextResponse.json(
        { error: "Reading progress not found" },
        { status: 404 }
      )
    }

    console.error("Error deleting reading progress:", error)
    return NextResponse.json(
      { error: "Failed to delete reading progress" },
      { status: 500 }
    )
  }
}

// Helper function to format reading time
function formatReadingTime(seconds: number): string {
  if (seconds < 60) {
    return `${seconds}s`
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60)
    return `${minutes}m`
  } else {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`
  }
}
