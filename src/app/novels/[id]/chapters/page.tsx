"use client"

import React from "react"
import { useParams } from "next/navigation"
import { useState } from "react"
import { useGetNovelQuery } from "@/store/api/novelsApi"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { 
  ArrowLeft, 
  BookOpen, 
  Clock, 
  User, 
  Calendar,
  Play,
  Eye,
  Lock
} from "lucide-react"
import Link from "next/link"
import { useSession } from "next-auth/react"

interface Chapter {
  id: string
  title: string
  order: number
  status: string
  createdAt: string
  updatedAt: string
}

export default function NovelChaptersPage() {
  const params = useParams()
  const novelId = params.id as string
  const { data: session } = useSession()
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [isLoadingChapters, setIsLoadingChapters] = useState(true)

  const { data: novel, isLoading: isLoadingNovel, error } = useGetNovelQuery(novelId)

  // Load chapters
  React.useEffect(() => {
    const loadChapters = async () => {
      try {
        setIsLoadingChapters(true)
        const response = await fetch(`/api/novels/${novelId}/chapters`)
        
        if (!response.ok) {
          throw new Error('Failed to load chapters')
        }
        
        const data = await response.json()
        setChapters(data.chapters || [])
      } catch (error) {
        console.error('Error loading chapters:', error)
      } finally {
        setIsLoadingChapters(false)
      }
    }

    if (novelId) {
      loadChapters()
    }
  }, [novelId])

  const isAuthor = session?.user?.id === novel?.authorId
  const isLoading = isLoadingNovel || isLoadingChapters

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  if (error || !novel) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Novel Not Found</h1>
          <p className="text-muted-foreground mb-4">
            The novel you're looking for doesn't exist or is not available.
          </p>
          <Link href="/browse">
            <Button>Browse Novels</Button>
          </Link>
        </div>
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getChapterStatusBadge = (status: string) => {
    switch (status) {
      case 'PUBLISHED':
        return <Badge variant="default">Published</Badge>
      case 'DRAFT':
        return <Badge variant="secondary">Draft</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const publishedChapters = chapters.filter(chapter => chapter.status === 'PUBLISHED')
  const totalChapters = isAuthor ? chapters.length : publishedChapters.length
  const displayChapters = isAuthor ? chapters : publishedChapters

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href={`/novels/${novelId}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Novel
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold">Table of Contents</h1>
            <p className="text-muted-foreground">{novel.title}</p>
          </div>
        </div>

        {/* Novel Info Card */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-start gap-6">
              {novel.coverImage && (
                <div className="flex-shrink-0">
                  <img
                    src={novel.coverImage}
                    alt={novel.title}
                    className="w-24 h-32 object-cover rounded-lg"
                  />
                </div>
              )}
              <div className="flex-1 space-y-3">
                <div>
                  <h2 className="text-xl font-bold">{novel.title}</h2>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <User className="h-4 w-4" />
                    <span>by {novel.author.name}</span>
                  </div>
                </div>
                
                {novel.description && (
                  <p className="text-muted-foreground">{novel.description}</p>
                )}
                
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <BookOpen className="h-4 w-4" />
                    <span>{totalChapters} chapters</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>Updated {formatDate(novel.updatedAt)}</span>
                  </div>
                  <Badge variant={novel.status === 'PUBLISHED' ? 'default' : 'secondary'}>
                    {novel.status}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Chapters List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Chapters ({displayChapters.length})</span>
              {isAuthor && (
                <Link href={`/dashboard/novels/${novelId}`}>
                  <Button variant="outline" size="sm">
                    Manage Chapters
                  </Button>
                </Link>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {displayChapters.length === 0 ? (
              <div className="text-center py-8">
                <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  {isAuthor ? "No chapters created yet" : "No published chapters available"}
                </p>
                {isAuthor && (
                  <Link href={`/dashboard/novels/${novelId}/chapters/new`}>
                    <Button className="mt-4">
                      Create First Chapter
                    </Button>
                  </Link>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                {displayChapters.map((chapter, index) => (
                  <div
                    key={chapter.id}
                    className="flex items-center justify-between p-4 rounded-lg border hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center gap-4 flex-1">
                      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                        {chapter.order}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium">{chapter.title}</h3>
                        <div className="flex items-center gap-3 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{formatDate(chapter.updatedAt)}</span>
                          </div>
                          {isAuthor && getChapterStatusBadge(chapter.status)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {chapter.status === 'PUBLISHED' ? (
                        <Link href={`/novels/${novelId}/chapters/${chapter.id}`}>
                          <Button size="sm">
                            <Play className="h-4 w-4 mr-2" />
                            Read
                          </Button>
                        </Link>
                      ) : isAuthor ? (
                        <Link href={`/dashboard/novels/${novelId}/chapters/${chapter.id}/edit`}>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                        </Link>
                      ) : (
                        <Button size="sm" variant="outline" disabled>
                          <Lock className="h-4 w-4 mr-2" />
                          Locked
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
