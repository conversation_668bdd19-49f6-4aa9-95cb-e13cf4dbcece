"use client"

import React, { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { NovelCard } from "@/components/novel/novel-card"
import { 
  Search, 
  BookOpen, 
  ArrowLeft, 
  Filter,
  SortAsc,
  SortDesc,
  Grid3X3,
  List
} from "lucide-react"
import Link from "next/link"

interface Novel {
  id: string
  title: string
  description: string | null
  coverImage: string | null
  status: string
  genre: string | null
  tags: string[]
  createdAt: string
  updatedAt: string
  author: {
    id: string
    name: string | null
    image: string | null
  }
  _count: {
    chapters: number
  }
}

interface GenrePageProps {
  params: {
    genre: string
  }
}

export default function GenrePage() {
  const params = useParams()
  const genreName = decodeURIComponent(params.genre as string)
  
  const [novels, setNovels] = useState<Novel[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState("updated")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [totalNovels, setTotalNovels] = useState(0)

  const limit = 12

  // Load novels for this genre
  const loadNovels = async (pageNum: number = 1, append: boolean = false) => {
    try {
      if (!append) setIsLoading(true)
      
      const params = new URLSearchParams({
        genre: genreName,
        page: pageNum.toString(),
        limit: limit.toString(),
        sortBy,
        sortOrder,
        ...(searchQuery && { q: searchQuery })
      })
      
      const response = await fetch(`/api/search/novels?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to load novels')
      }
      
      const data = await response.json()
      
      if (append) {
        setNovels(prev => [...prev, ...data.novels])
      } else {
        setNovels(data.novels)
      }
      
      setTotalNovels(data.pagination.total)
      setHasMore(data.pagination.hasNext)
    } catch (error) {
      console.error('Error loading novels:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Initial load and when filters change
  useEffect(() => {
    setPage(1)
    loadNovels(1, false)
  }, [genreName, searchQuery, sortBy, sortOrder])

  // Load more novels
  const loadMore = () => {
    const nextPage = page + 1
    setPage(nextPage)
    loadNovels(nextPage, true)
  }

  const formatGenreName = (genre: string) => {
    return genre.charAt(0).toUpperCase() + genre.slice(1).replace(/-/g, ' ')
  }

  const getSortLabel = () => {
    const labels: Record<string, string> = {
      title: "Title",
      author: "Author",
      created: "Date Created",
      updated: "Last Updated",
      chapters: "Chapter Count"
    }
    return labels[sortBy] || "Relevance"
  }

  if (isLoading && novels.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/genres">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              All Genres
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-4xl font-bold">{formatGenreName(genreName)}</h1>
            <p className="text-muted-foreground">
              {totalNovels} novels in this genre
            </p>
          </div>
        </div>

        {/* Controls */}
        <div className="flex flex-col lg:flex-row gap-4 mb-8">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={`Search ${formatGenreName(genreName)} novels...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="updated">Last Updated</option>
              <option value="created">Newest</option>
              <option value="title">Title</option>
              <option value="author">Author</option>
              <option value="chapters">Chapter Count</option>
            </select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
            >
              {sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
            </Button>
            
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Results Info */}
        <div className="flex items-center justify-between mb-6">
          <p className="text-sm text-muted-foreground">
            Showing {novels.length} of {totalNovels} novels
            {searchQuery && ` matching "${searchQuery}"`}
          </p>
          <p className="text-sm text-muted-foreground">
            Sorted by {getSortLabel()} ({sortOrder === "asc" ? "ascending" : "descending"})
          </p>
        </div>

        {/* Novels Grid/List */}
        {novels.length === 0 ? (
          <div className="text-center py-12">
            <BookOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">No novels found</h3>
            <p className="text-muted-foreground">
              {searchQuery 
                ? `No ${formatGenreName(genreName)} novels match your search` 
                : `No novels available in ${formatGenreName(genreName)} yet`
              }
            </p>
            {searchQuery && (
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => setSearchQuery("")}
              >
                Clear Search
              </Button>
            )}
          </div>
        ) : viewMode === "grid" ? (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {novels.map((novel) => (
                <NovelCard key={novel.id} novel={novel} />
              ))}
            </div>
            
            {hasMore && (
              <div className="text-center">
                <Button onClick={loadMore} disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <LoadingSpinner className="h-4 w-4 mr-2" />
                      Loading...
                    </>
                  ) : (
                    "Load More"
                  )}
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {novels.map((novel) => (
              <Card key={novel.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex gap-4">
                    {novel.coverImage && (
                      <div className="flex-shrink-0">
                        <img
                          src={novel.coverImage}
                          alt={novel.title}
                          className="w-16 h-20 object-cover rounded"
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <Link href={`/novels/${novel.id}`}>
                            <h3 className="font-semibold hover:text-primary transition-colors">
                              {novel.title}
                            </h3>
                          </Link>
                          <p className="text-sm text-muted-foreground">
                            by {novel.author.name}
                          </p>
                        </div>
                        <Badge variant="outline">{novel.status}</Badge>
                      </div>
                      
                      {novel.description && (
                        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                          {novel.description}
                        </p>
                      )}
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>{novel._count.chapters} chapters</span>
                          <span>Updated {new Date(novel.updatedAt).toLocaleDateString()}</span>
                        </div>
                        <Link href={`/novels/${novel.id}`}>
                          <Button size="sm" variant="outline">
                            Read Now
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
            
            {hasMore && (
              <div className="text-center pt-4">
                <Button onClick={loadMore} disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <LoadingSpinner className="h-4 w-4 mr-2" />
                      Loading...
                    </>
                  ) : (
                    "Load More"
                  )}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
