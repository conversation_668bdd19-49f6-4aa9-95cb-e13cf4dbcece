"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { 
  Search, 
  BookOpen, 
  TrendingUp, 
  Filter,
  Grid3X3,
  List
} from "lucide-react"
import Link from "next/link"

interface Genre {
  genre: string
  count: number
  description?: string
  trending?: boolean
}

export default function GenresPage() {
  const [genres, setGenres] = useState<Genre[]>([])
  const [filteredGenres, setFilteredGenres] = useState<Genre[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [sortBy, setSortBy] = useState<"name" | "count">("count")

  // Load genres
  useEffect(() => {
    const loadGenres = async () => {
      try {
        setIsLoading(true)
        const response = await fetch("/api/search/novels?limit=1") // Just to get facets
        
        if (!response.ok) {
          throw new Error('Failed to load genres')
        }
        
        const data = await response.json()
        const genresData = data.facets?.genres || []
        
        // Add some predefined popular genres with descriptions
        const genreDescriptions: Record<string, string> = {
          "Fantasy": "Magical worlds, mythical creatures, and supernatural adventures",
          "Romance": "Love stories, relationships, and emotional journeys",
          "Mystery": "Puzzles, detective work, and suspenseful investigations",
          "Science Fiction": "Future technology, space exploration, and scientific concepts",
          "Thriller": "High-stakes tension, danger, and edge-of-your-seat excitement",
          "Horror": "Scary stories, supernatural terror, and psychological fear",
          "Adventure": "Exciting journeys, exploration, and daring quests",
          "Drama": "Character-driven stories with emotional depth",
          "Comedy": "Humorous tales designed to entertain and amuse",
          "Historical Fiction": "Stories set in the past with historical accuracy",
          "Young Adult": "Coming-of-age stories for teenage and young adult readers",
          "Contemporary": "Modern-day stories reflecting current times and issues"
        }
        
        const enrichedGenres = genresData.map((g: any) => ({
          genre: g.genre,
          count: g.count,
          description: genreDescriptions[g.genre] || `Discover ${g.genre.toLowerCase()} novels`,
          trending: g.count > 5 // Mark as trending if more than 5 novels
        }))
        
        setGenres(enrichedGenres)
        setFilteredGenres(enrichedGenres)
      } catch (error) {
        console.error('Error loading genres:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadGenres()
  }, [])

  // Filter and sort genres
  useEffect(() => {
    let filtered = genres.filter(genre =>
      genre.genre.toLowerCase().includes(searchQuery.toLowerCase())
    )

    // Sort genres
    filtered.sort((a, b) => {
      if (sortBy === "name") {
        return a.genre.localeCompare(b.genre)
      } else {
        return b.count - a.count
      }
    })

    setFilteredGenres(filtered)
  }, [genres, searchQuery, sortBy])

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Browse by Genre</h1>
          <p className="text-xl text-muted-foreground">
            Discover novels across different genres and find your next favorite read
          </p>
        </div>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search genres..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as "name" | "count")}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="count">Most Popular</option>
              <option value="name">Alphabetical</option>
            </select>
            
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Genres</p>
                  <p className="text-2xl font-bold">{filteredGenres.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Trending Genres</p>
                  <p className="text-2xl font-bold">
                    {filteredGenres.filter(g => g.trending).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Filter className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Novels</p>
                  <p className="text-2xl font-bold">
                    {filteredGenres.reduce((sum, g) => sum + g.count, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Genres Grid/List */}
        {filteredGenres.length === 0 ? (
          <div className="text-center py-12">
            <BookOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">No genres found</h3>
            <p className="text-muted-foreground">
              {searchQuery ? "Try adjusting your search terms" : "No genres available yet"}
            </p>
          </div>
        ) : viewMode === "grid" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredGenres.map((genre) => (
              <Link key={genre.genre} href={`/genres/${encodeURIComponent(genre.genre.toLowerCase())}`}>
                <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{genre.genre}</CardTitle>
                      {genre.trending && (
                        <Badge variant="secondary" className="text-xs">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          Trending
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground text-sm mb-4">
                      {genre.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <BookOpen className="h-4 w-4" />
                        <span>{genre.count} novels</span>
                      </div>
                      <Button size="sm" variant="outline">
                        Explore
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredGenres.map((genre) => (
              <Link key={genre.genre} href={`/genres/${encodeURIComponent(genre.genre.toLowerCase())}`}>
                <Card className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold">{genre.genre}</h3>
                          {genre.trending && (
                            <Badge variant="secondary" className="text-xs">
                              <TrendingUp className="h-3 w-3 mr-1" />
                              Trending
                            </Badge>
                          )}
                        </div>
                        <p className="text-muted-foreground text-sm mb-2">
                          {genre.description}
                        </p>
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <BookOpen className="h-4 w-4" />
                          <span>{genre.count} novels available</span>
                        </div>
                      </div>
                      <Button variant="outline">
                        Explore Genre
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
