"use client"

import React, { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { 
  BookOpen, 
  Clock, 
  Calendar,
  TrendingUp,
  Play,
  Trash2,
  BarChart3,
  User
} from "lucide-react"
import Link from "next/link"
import { useToast } from "@/hooks/use-toast"

interface ReadingProgressItem {
  id: string
  novelId: string
  progress: number
  lastReadAt: string
  totalTimeRead: number
  novel: {
    id: string
    title: string
    coverImage: string | null
    status: string
    author: {
      id: string
      name: string | null
      image: string | null
    }
    _count: {
      chapters: number
    }
  }
  chapter: {
    id: string
    title: string
    order: number
  } | null
  lastChapter: {
    id: string
    title: string
    order: number
  } | null
}

export default function ReadingHistoryPage() {
  const { data: session, status } = useSession()
  const { toast } = useToast()
  const [readingHistory, setReadingHistory] = useState<ReadingProgressItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState({
    totalNovels: 0,
    totalTimeRead: 0,
    averageProgress: 0,
    completedNovels: 0,
  })

  // Load reading history
  const loadReadingHistory = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/reading-progress?limit=50")
      
      if (!response.ok) {
        throw new Error('Failed to load reading history')
      }
      
      const data = await response.json()
      setReadingHistory(data.readingProgress || [])
      
      // Calculate stats
      const history = data.readingProgress || []
      const totalNovels = history.length
      const totalTimeRead = history.reduce((sum: number, item: ReadingProgressItem) => sum + item.totalTimeRead, 0)
      const averageProgress = totalNovels > 0 
        ? Math.round(history.reduce((sum: number, item: ReadingProgressItem) => sum + item.progress, 0) / totalNovels)
        : 0
      const completedNovels = history.filter((item: ReadingProgressItem) => item.progress >= 100).length
      
      setStats({
        totalNovels,
        totalTimeRead,
        averageProgress,
        completedNovels,
      })
    } catch (error) {
      console.error('Error loading reading history:', error)
      toast({
        title: "Error",
        description: "Failed to load reading history",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (status === "authenticated") {
      loadReadingHistory()
    }
  }, [status])

  const handleDeleteProgress = async (novelId: string) => {
    try {
      const response = await fetch(`/api/reading-progress/${novelId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error('Failed to delete reading progress')
      }

      toast({
        title: "Success",
        description: "Reading progress deleted successfully",
      })

      // Reload history
      await loadReadingHistory()
    } catch (error) {
      console.error('Error deleting reading progress:', error)
      toast({
        title: "Error",
        description: "Failed to delete reading progress",
        variant: "destructive",
      })
    }
  }

  const formatReadingTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}s`
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60)
      return `${minutes}m`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (status === "loading" || isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Sign In Required</h1>
          <p className="text-muted-foreground mb-4">
            Please sign in to view your reading history.
          </p>
          <Link href="/auth/signin">
            <Button>Sign In</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-2">Reading History</h1>
          <p className="text-muted-foreground">
            Track your reading progress and discover your reading patterns
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Novels Started</p>
                  <p className="text-2xl font-bold">{stats.totalNovels}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Time Read</p>
                  <p className="text-2xl font-bold">{formatReadingTime(stats.totalTimeRead)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Avg Progress</p>
                  <p className="text-2xl font-bold">{stats.averageProgress}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-primary" />
                <div>
                  <p className="text-sm text-muted-foreground">Completed</p>
                  <p className="text-2xl font-bold">{stats.completedNovels}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Reading History */}
        <Card>
          <CardHeader>
            <CardTitle>Your Reading Progress</CardTitle>
          </CardHeader>
          <CardContent>
            {readingHistory.length === 0 ? (
              <div className="text-center py-8">
                <BookOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No reading history yet</h3>
                <p className="text-muted-foreground mb-4">
                  Start reading some novels to see your progress here
                </p>
                <Link href="/browse">
                  <Button>Browse Novels</Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {readingHistory.map((item) => (
                  <div key={item.id} className="flex gap-4 p-4 rounded-lg border">
                    {/* Cover Image */}
                    <div className="flex-shrink-0">
                      {item.novel.coverImage ? (
                        <img
                          src={item.novel.coverImage}
                          alt={item.novel.title}
                          className="w-16 h-20 object-cover rounded"
                        />
                      ) : (
                        <div className="w-16 h-20 bg-muted rounded flex items-center justify-center">
                          <BookOpen className="h-6 w-6 text-muted-foreground" />
                        </div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 space-y-2">
                      <div className="flex items-start justify-between">
                        <div>
                          <Link href={`/novels/${item.novel.id}`}>
                            <h3 className="font-semibold hover:text-primary transition-colors">
                              {item.novel.title}
                            </h3>
                          </Link>
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            <User className="h-3 w-3" />
                            <span>by {item.novel.author.name}</span>
                          </div>
                        </div>
                        <Badge variant={item.novel.status === 'PUBLISHED' ? 'default' : 'secondary'}>
                          {item.novel.status}
                        </Badge>
                      </div>

                      {/* Progress */}
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span>Progress: {item.progress}%</span>
                          <span>{item.lastChapter?.order || 0} / {item.novel._count.chapters} chapters</span>
                        </div>
                        <Progress value={item.progress} className="h-2" />
                      </div>

                      {/* Stats and Actions */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{formatReadingTime(item.totalTimeRead)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>Last read {formatDate(item.lastReadAt)}</span>
                          </div>
                          {item.lastChapter && (
                            <span>Last: {item.lastChapter.title}</span>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Link href={`/novels/${item.novel.id}/chapters`}>
                            <Button size="sm" variant="outline">
                              <Play className="h-4 w-4 mr-2" />
                              Continue
                            </Button>
                          </Link>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeleteProgress(item.novel.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
