"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { AuthorGuard } from "@/components/auth/auth-guard"
import { FileUpload } from "@/components/ui/file-upload"
import { useGetNovelQuery } from "@/store/api/novelsApi"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { ArrowLeft, Save, AlertCircle } from "lucide-react"
import Link from "next/link"
import { useToast } from "@/hooks/use-toast"

export default function CoverUploadPage() {
  const params = useParams()
  const router = useRouter()
  const novelId = params.id as string
  const { toast } = useToast()

  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)

  const { data: novel, isLoading, error, refetch } = useGetNovelQuery(novelId)

  const handleFileSelect = (file: File) => {
    setSelectedFile(file)
    setUploadError(null)
  }

  const handleFileRemove = () => {
    setSelectedFile(null)
    setUploadError(null)
  }

  const handleUpload = async () => {
    if (!selectedFile) return

    setIsUploading(true)
    setUploadError(null)

    try {
      const formData = new FormData()
      formData.append("file", selectedFile)
      formData.append("novelId", novelId)

      const response = await fetch("/api/upload/cover", {
        method: "POST",
        body: formData,
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Upload failed")
      }

      toast({
        title: "Success",
        description: "Cover image uploaded successfully!",
      })

      // Refresh novel data
      await refetch()
      
      // Clear selected file
      setSelectedFile(null)
      
      // Redirect back to novel management
      router.push(`/dashboard/novels/${novelId}`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Upload failed"
      setUploadError(errorMessage)
      toast({
        title: "Upload Failed",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveCover = async () => {
    if (!novel?.coverImage) return

    setIsUploading(true)
    setUploadError(null)

    try {
      // Extract filename from URL
      const url = new URL(novel.coverImage)
      const filename = url.pathname.split('/').pop()

      if (!filename) {
        throw new Error("Invalid cover image URL")
      }

      const response = await fetch(`/api/upload/cover/${filename}`, {
        method: "DELETE",
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Delete failed")
      }

      toast({
        title: "Success",
        description: "Cover image removed successfully!",
      })

      // Refresh novel data
      await refetch()
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Delete failed"
      setUploadError(errorMessage)
      toast({
        title: "Delete Failed",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  if (isLoading) {
    return (
      <AuthorGuard>
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center">
            <LoadingSpinner />
          </div>
        </div>
      </AuthorGuard>
    )
  }

  if (error || !novel) {
    return (
      <AuthorGuard>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h1 className="text-2xl font-bold mb-2">Novel Not Found</h1>
            <p className="text-muted-foreground mb-4">
              The novel you're looking for doesn't exist or you don't have permission to edit it.
            </p>
            <Link href="/dashboard">
              <Button>Back to Dashboard</Button>
            </Link>
          </div>
        </div>
      </AuthorGuard>
    )
  }

  return (
    <AuthorGuard>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Link href={`/dashboard/novels/${novelId}`}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Novel
              </Button>
            </Link>
            <div className="flex-1">
              <h1 className="text-3xl font-bold">Cover Image</h1>
              <p className="text-muted-foreground">{novel.title}</p>
            </div>
          </div>

          {/* Upload Card */}
          <Card>
            <CardHeader>
              <CardTitle>Upload Cover Image</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <FileUpload
                onFileSelect={handleFileSelect}
                onFileRemove={novel.coverImage ? handleRemoveCover : undefined}
                currentFile={selectedFile}
                currentImageUrl={novel.coverImage}
                disabled={isUploading}
                error={uploadError}
              />

              {/* Upload Button */}
              {selectedFile && (
                <div className="flex justify-end">
                  <Button
                    onClick={handleUpload}
                    disabled={isUploading}
                    className="min-w-[120px]"
                  >
                    {isUploading ? (
                      <>
                        <LoadingSpinner className="h-4 w-4 mr-2" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Upload Cover
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthorGuard>
  )
}
