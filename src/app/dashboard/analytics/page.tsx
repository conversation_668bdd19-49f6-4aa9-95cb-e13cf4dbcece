"use client"

import React, { useState, useEffect } from "react"
import { AuthorGuard } from "@/components/auth/auth-guard"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Clock, 
  BookOpen,
  Eye,
  AlertTriangle,
  CheckCircle,
  Info,
  Calendar,
  Target
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface NovelAnalytics {
  novel: {
    id: string
    title: string
    status: string
    coverImage: string | null
  }
  metrics: {
    totalChapters: number
    publishedChapters: number
    totalFollowers: number
    uniqueReaders: number
    totalReadingTime: number
    averageProgress: number
    engagementRate: number
  }
  trends: {
    dailyReads: Array<{ date: string; count: number }>
    dailyFollowers: Array<{ date: string; count: number }>
  }
}

interface ChapterInsight {
  type: "warning" | "success" | "info"
  title: string
  description: string
  data: Array<{
    chapter: string
    novel: string
    dropOffRate?: number
    averageTime?: number
    completionRate?: number
  }>
}

export default function AnalyticsDashboardPage() {
  const { toast } = useToast()
  const [timeframe, setTimeframe] = useState("30d")
  const [isLoading, setIsLoading] = useState(true)
  const [novelAnalytics, setNovelAnalytics] = useState<{
    overview: any
    novels: NovelAnalytics[]
  } | null>(null)
  const [chapterInsights, setChapterInsights] = useState<ChapterInsight[]>([])

  // Load analytics data
  const loadAnalytics = async () => {
    try {
      setIsLoading(true)
      
      const [novelsResponse, chaptersResponse] = await Promise.all([
        fetch(`/api/analytics/novels?timeframe=${timeframe}`),
        fetch(`/api/analytics/chapters?timeframe=${timeframe}`)
      ])

      if (!novelsResponse.ok || !chaptersResponse.ok) {
        throw new Error('Failed to load analytics')
      }

      const novelsData = await novelsResponse.json()
      const chaptersData = await chaptersResponse.json()

      setNovelAnalytics(novelsData)
      setChapterInsights(chaptersData.insights || [])
    } catch (error) {
      console.error('Error loading analytics:', error)
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadAnalytics()
  }, [timeframe])

  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}s`
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60)
      return `${minutes}m`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`
    }
  }

  const getTimeframeLabel = (tf: string) => {
    switch (tf) {
      case "7d": return "Last 7 days"
      case "30d": return "Last 30 days"
      case "90d": return "Last 90 days"
      case "1y": return "Last year"
      default: return "Last 30 days"
    }
  }

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "warning": return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case "success": return <CheckCircle className="h-5 w-5 text-green-500" />
      case "info": return <Info className="h-5 w-5 text-blue-500" />
      default: return <Info className="h-5 w-5" />
    }
  }

  if (isLoading) {
    return (
      <AuthorGuard>
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center">
            <LoadingSpinner />
          </div>
        </div>
      </AuthorGuard>
    )
  }

  return (
    <AuthorGuard>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
              <p className="text-muted-foreground">
                Track your novel performance and reader engagement
              </p>
            </div>
            <div className="flex items-center gap-2">
              <select
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="px-3 py-2 border rounded-md bg-background"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">Last year</option>
              </select>
            </div>
          </div>

          {/* Overview Stats */}
          {novelAnalytics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5 text-primary" />
                    <div>
                      <p className="text-sm text-muted-foreground">Total Novels</p>
                      <p className="text-2xl font-bold">{novelAnalytics.overview.totalNovels}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-primary" />
                    <div>
                      <p className="text-sm text-muted-foreground">Total Readers</p>
                      <p className="text-2xl font-bold">{novelAnalytics.overview.totalReaders}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Clock className="h-5 w-5 text-primary" />
                    <div>
                      <p className="text-sm text-muted-foreground">Reading Time</p>
                      <p className="text-2xl font-bold">{formatTime(novelAnalytics.overview.totalReadingTime)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-primary" />
                    <div>
                      <p className="text-sm text-muted-foreground">Total Followers</p>
                      <p className="text-2xl font-bold">{novelAnalytics.overview.totalFollowers}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Insights */}
          {chapterInsights.length > 0 && (
            <div className="mb-8">
              <h2 className="text-2xl font-bold mb-4">Performance Insights</h2>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {chapterInsights.map((insight, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-lg">
                        {getInsightIcon(insight.type)}
                        {insight.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-3">
                        {insight.description}
                      </p>
                      <div className="space-y-2">
                        {insight.data.slice(0, 3).map((item, itemIndex) => (
                          <div key={itemIndex} className="flex justify-between items-center text-sm">
                            <span className="truncate">{item.chapter}</span>
                            <span className="font-medium">
                              {item.dropOffRate && `${item.dropOffRate}%`}
                              {item.averageTime && `${item.averageTime}m`}
                              {item.completionRate && `${item.completionRate}%`}
                            </span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Novel Performance */}
          {novelAnalytics && (
            <div>
              <h2 className="text-2xl font-bold mb-4">Novel Performance</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {novelAnalytics.novels.map((novel) => (
                  <Card key={novel.novel.id}>
                    <CardHeader>
                      <div className="flex items-start gap-3">
                        {novel.novel.coverImage && (
                          <img
                            src={novel.novel.coverImage}
                            alt={novel.novel.title}
                            className="w-12 h-16 object-cover rounded"
                          />
                        )}
                        <div className="flex-1">
                          <CardTitle className="text-lg">{novel.novel.title}</CardTitle>
                          <Badge variant={novel.novel.status === 'PUBLISHED' ? 'default' : 'secondary'}>
                            {novel.novel.status}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Chapters</p>
                          <p className="font-medium">{novel.metrics.publishedChapters}/{novel.metrics.totalChapters}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Followers</p>
                          <p className="font-medium">{novel.metrics.totalFollowers}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Readers</p>
                          <p className="font-medium">{novel.metrics.uniqueReaders}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Engagement</p>
                          <p className="font-medium">{novel.metrics.engagementRate}%</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Avg Progress</p>
                          <p className="font-medium">{novel.metrics.averageProgress}%</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Reading Time</p>
                          <p className="font-medium">{formatTime(novel.metrics.totalReadingTime)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Empty State */}
          {novelAnalytics && novelAnalytics.novels.length === 0 && (
            <div className="text-center py-12">
              <BarChart3 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No analytics data yet</h3>
              <p className="text-muted-foreground mb-4">
                Create and publish novels to start seeing analytics data for {getTimeframeLabel(timeframe).toLowerCase()}
              </p>
              <Button asChild>
                <a href="/dashboard/novels/new">Create Your First Novel</a>
              </Button>
            </div>
          )}
        </div>
      </div>
    </AuthorGuard>
  )
}
