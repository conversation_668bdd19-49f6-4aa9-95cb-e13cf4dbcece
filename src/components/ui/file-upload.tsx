"use client"

import React, { useCallback, useState } from "react"
import { useDropzone } from "react-dropzone"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Upload, X, Image, AlertCircle } from "lucide-react"

interface FileUploadProps {
  onFileSelect: (file: File) => void
  onFileRemove?: () => void
  accept?: Record<string, string[]>
  maxSize?: number
  currentFile?: File | null
  currentImageUrl?: string | null
  disabled?: boolean
  className?: string
  error?: string | null
}

export function FileUpload({
  onFileSelect,
  onFileRemove,
  accept = {
    "image/*": [".jpg", ".jpeg", ".png", ".webp"]
  },
  maxSize = 5 * 1024 * 1024, // 5MB
  currentFile,
  currentImageUrl,
  disabled = false,
  className,
  error
}: FileUploadProps) {
  const [dragActive, setDragActive] = useState(false)

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        onFileSelect(acceptedFiles[0])
      }
    },
    [onFileSelect]
  )

  const { getRootProps, getInputProps, isDragActive, fileRejections } = useDropzone({
    onDrop,
    accept,
    maxSize,
    multiple: false,
    disabled,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
  })

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  const hasFile = currentFile || currentImageUrl
  const rejectionError = fileRejections[0]?.errors[0]?.message

  return (
    <div className={cn("w-full", className)}>
      <Card className={cn(
        "border-2 border-dashed transition-colors",
        isDragActive && "border-primary bg-primary/5",
        error && "border-destructive",
        disabled && "opacity-50 cursor-not-allowed"
      )}>
        <CardContent className="p-6">
          {hasFile ? (
            <div className="space-y-4">
              {/* Preview */}
              <div className="flex items-center justify-center">
                {currentImageUrl ? (
                  <div className="relative">
                    <img
                      src={currentImageUrl}
                      alt="Cover preview"
                      className="max-w-full max-h-48 rounded-lg object-cover"
                    />
                  </div>
                ) : currentFile ? (
                  <div className="flex items-center space-x-3 p-4 bg-muted rounded-lg">
                    <Image className="h-8 w-8 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">{currentFile.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatFileSize(currentFile.size)}
                      </p>
                    </div>
                  </div>
                ) : null}
              </div>

              {/* Actions */}
              <div className="flex justify-center space-x-2">
                <div {...getRootProps()}>
                  <input {...getInputProps()} />
                  <Button variant="outline" size="sm" disabled={disabled}>
                    <Upload className="h-4 w-4 mr-2" />
                    Replace
                  </Button>
                </div>
                {onFileRemove && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onFileRemove}
                    disabled={disabled}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Remove
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div {...getRootProps()} className="cursor-pointer">
              <input {...getInputProps()} />
              <div className="flex flex-col items-center justify-center space-y-4 py-8">
                <div className={cn(
                  "rounded-full p-4 transition-colors",
                  isDragActive ? "bg-primary text-primary-foreground" : "bg-muted"
                )}>
                  <Upload className="h-8 w-8" />
                </div>
                
                <div className="text-center space-y-2">
                  <p className="text-lg font-medium">
                    {isDragActive ? "Drop your image here" : "Upload cover image"}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Drag and drop an image, or click to browse
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Supports JPG, PNG, WebP up to {formatFileSize(maxSize)}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Error Messages */}
          {(error || rejectionError) && (
            <div className="mt-4 flex items-center space-x-2 text-sm text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span>{error || rejectionError}</span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
