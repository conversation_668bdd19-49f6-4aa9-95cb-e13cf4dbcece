"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import Link from "next/link"
import { PenTool, BookOpen, Users, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export function BeginWritingButton() {
  const { data: session, status, update } = useSession()
  const router = useRouter()
  const { toast } = useToast()
  const [isUpgrading, setIsUpgrading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)

  const handleUpgradeToAuthor = async () => {
    try {
      setIsUpgrading(true)

      const response = await fetch("/api/user/upgrade-to-author", {
        method: "POST",
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Failed to upgrade to author")
      }

      toast({
        title: "Welcome, Author! 🎉",
        description: result.message,
      })

      // Update the session to reflect the new role
      await update()

      // Close dialog and redirect to dashboard
      setIsOpen(false)
      router.push("/dashboard")
    } catch (error) {
      console.error("Error upgrading to author:", error)
      toast({
        title: "Upgrade Failed",
        description: error instanceof Error ? error.message : "Failed to upgrade to author",
        variant: "destructive",
      })
    } finally {
      setIsUpgrading(false)
    }
  }

  // Loading state
  if (status === "loading") {
    return (
      <Button variant="outline" size="lg" className="text-lg px-8" disabled>
        Begin Writing
      </Button>
    )
  }

  // Not authenticated - redirect to sign in
  if (!session) {
    return (
      <Button asChild variant="outline" size="lg" className="text-lg px-8">
        <Link href="/auth/signin">
          Begin Writing
        </Link>
      </Button>
    )
  }

  // Already an author - go to dashboard
  if (session.user.role === "AUTHOR") {
    return (
      <Button asChild variant="outline" size="lg" className="text-lg px-8">
        <Link href="/dashboard">
          Go to Dashboard
        </Link>
      </Button>
    )
  }

  // Reader who wants to become an author
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="lg" className="text-lg px-8">
          Begin Writing
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <PenTool className="h-5 w-5" />
            Become an Author
          </DialogTitle>
          <DialogDescription>
            Ready to share your stories with the world? Here's how to get started as an author on Black Blogs.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            <p className="mb-4">
              Ready to share your stories with the world? Upgrade your account to start writing and publishing novels on Black Blogs.
            </p>

            <div className="bg-muted/50 rounded-lg p-4 space-y-2">
              <h4 className="font-medium text-foreground">What you'll get as an author:</h4>
              <ul className="space-y-1 text-xs">
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  Create and publish unlimited novels
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  Upload custom cover images
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  Track reader analytics and engagement
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  Manage chapters and publishing schedule
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  Connect with your readers
                </li>
              </ul>
            </div>
          </div>

          <div className="flex flex-col gap-2 pt-4">
            <Button
              onClick={handleUpgradeToAuthor}
              disabled={isUpgrading}
              className="w-full"
            >
              {isUpgrading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Upgrading...
                </>
              ) : (
                <>
                  <PenTool className="h-4 w-4 mr-2" />
                  Become an Author
                </>
              )}
            </Button>

            <Button asChild variant="outline" className="w-full">
              <Link href="/browse">
                <BookOpen className="h-4 w-4 mr-2" />
                Continue Reading
              </Link>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
