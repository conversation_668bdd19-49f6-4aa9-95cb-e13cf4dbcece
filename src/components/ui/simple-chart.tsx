"use client"

import React from "react"
import { cn } from "@/lib/utils"

interface DataPoint {
  date: string
  count: number
  label?: string
}

interface SimpleChartProps {
  data: DataPoint[]
  height?: number
  className?: string
  color?: string
  showLabels?: boolean
}

export function SimpleChart({ 
  data, 
  height = 100, 
  className,
  color = "hsl(var(--primary))",
  showLabels = false 
}: SimpleChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className={cn("flex items-center justify-center text-muted-foreground", className)} style={{ height }}>
        No data available
      </div>
    )
  }

  const maxValue = Math.max(...data.map(d => d.count))
  const minValue = Math.min(...data.map(d => d.count))
  const range = maxValue - minValue || 1

  return (
    <div className={cn("relative", className)}>
      <svg width="100%" height={height} className="overflow-visible">
        {/* Grid lines */}
        <defs>
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="hsl(var(--border))" strokeWidth="0.5" opacity="0.3"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
        
        {/* Chart area */}
        <g>
          {data.map((point, index) => {
            const x = (index / (data.length - 1)) * 100
            const y = ((maxValue - point.count) / range) * (height - 20) + 10
            const barHeight = ((point.count - minValue) / range) * (height - 20)
            
            return (
              <g key={index}>
                {/* Bar */}
                <rect
                  x={`${x - 1}%`}
                  y={y}
                  width="2%"
                  height={barHeight}
                  fill={color}
                  opacity="0.7"
                  className="hover:opacity-100 transition-opacity"
                />
                
                {/* Data point */}
                <circle
                  cx={`${x}%`}
                  cy={y}
                  r="2"
                  fill={color}
                  className="hover:r-3 transition-all"
                />
                
                {/* Label */}
                {showLabels && (
                  <text
                    x={`${x}%`}
                    y={height - 5}
                    textAnchor="middle"
                    fontSize="10"
                    fill="hsl(var(--muted-foreground))"
                  >
                    {new Date(point.date).getDate()}
                  </text>
                )}
              </g>
            )
          })}
          
          {/* Trend line */}
          <polyline
            fill="none"
            stroke={color}
            strokeWidth="2"
            points={data.map((point, index) => {
              const x = (index / (data.length - 1)) * 100
              const y = ((maxValue - point.count) / range) * (height - 20) + 10
              return `${x},${y}`
            }).join(' ')}
          />
        </g>
      </svg>
      
      {/* Tooltip area (could be enhanced with a proper tooltip library) */}
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
        {/* This could be enhanced with hover tooltips showing exact values */}
      </div>
    </div>
  )
}

interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  trend?: "up" | "down" | "neutral"
  icon?: React.ReactNode
  className?: string
}

export function MetricCard({ 
  title, 
  value, 
  change, 
  trend = "neutral", 
  icon, 
  className 
}: MetricCardProps) {
  const getTrendColor = () => {
    switch (trend) {
      case "up": return "text-green-600"
      case "down": return "text-red-600"
      default: return "text-muted-foreground"
    }
  }

  const getTrendIcon = () => {
    switch (trend) {
      case "up": return "↗"
      case "down": return "↘"
      default: return "→"
    }
  }

  return (
    <div className={cn("p-4 rounded-lg border bg-card", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {icon}
          <span className="text-sm font-medium text-muted-foreground">{title}</span>
        </div>
        {change !== undefined && (
          <span className={cn("text-xs font-medium", getTrendColor())}>
            {getTrendIcon()} {Math.abs(change)}%
          </span>
        )}
      </div>
      <div className="mt-2">
        <span className="text-2xl font-bold">{value}</span>
      </div>
    </div>
  )
}
