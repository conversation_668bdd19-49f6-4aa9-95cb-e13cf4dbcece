"use client"

import Image from "next/image"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { LibraryButton } from "@/components/library/library-button"
import { formatDate } from "@/lib/utils"
import { BookOpen, Calendar, User, ArrowLeft, Play } from "lucide-react"

interface NovelDetailProps {
  novel: {
    id: string
    title: string
    description?: string | null
    synopsis?: string | null
    coverImage?: string | null
    genre?: string | null
    tags: string[]
    status: string
    createdAt: string | Date
    publishedAt?: string | Date | null
    author: {
      id: string
      name?: string | null
      image?: string | null
      bio?: string | null
    }
    chapters: Array<{
      id: string
      title: string
      order: number
      createdAt: string | Date
    }>
    _count: {
      chapters: number
    }
  }
}

export function NovelDetail({ novel }: NovelDetailProps) {
  const firstChapter = novel.chapters.find(chapter => chapter.order === 1)

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-8">
        {/* Back Button */}
        <Button variant="ghost" asChild className="mb-4">
          <Link href="/browse">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Browse
          </Link>
        </Button>

        {/* Novel Header */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cover Image */}
          <div className="lg:col-span-1">
            <div className="relative aspect-[3/4] w-full max-w-sm mx-auto">
              <Image
                src={novel.coverImage || "/placeholder-cover.svg"}
                alt={novel.title}
                fill
                className="object-cover rounded-lg shadow-lg"
                priority
              />
            </div>
          </div>

          {/* Novel Info */}
          <div className="lg:col-span-2 space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <h1 className="text-4xl font-bold tracking-tight">{novel.title}</h1>

                <div className="flex items-center gap-4 text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={novel.author.image || ""} alt={novel.author.name || ""} />
                      <AvatarFallback className="text-xs">
                        {novel.author.name?.[0]?.toUpperCase() || "A"}
                      </AvatarFallback>
                    </Avatar>
                    <Link
                      href={`/authors/${novel.author.id}`}
                      className="hover:text-foreground transition-colors"
                    >
                      by {novel.author.name}
                    </Link>
                  </div>

                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(novel.publishedAt || novel.createdAt)}</span>
                  </div>
                </div>
              </div>

              {/* Badges */}
              <div className="flex flex-wrap gap-2">
                <Badge variant={novel.status === "PUBLISHED" ? "default" : "secondary"}>
                  {novel.status.toLowerCase()}
                </Badge>
                {novel.genre && (
                  <Badge variant="outline">{novel.genre}</Badge>
                )}
                <Badge variant="outline">
                  <BookOpen className="mr-1 h-3 w-3" />
                  {novel._count.chapters} chapters
                </Badge>
              </div>

              {/* Description */}
              {novel.description && (
                <p className="text-lg text-muted-foreground leading-relaxed">
                  {novel.description}
                </p>
              )}

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-3">
                {firstChapter && (
                  <Button asChild size="lg">
                    <Link href={`/novels/${novel.id}/chapters/${firstChapter.id}`}>
                      <Play className="mr-2 h-4 w-4" />
                      Start Reading
                    </Link>
                  </Button>
                )}

                <LibraryButton
                  novelId={novel.id}
                  variant="outline"
                  size="lg"
                />
              </div>

              {/* Tags */}
              {novel.tags && novel.tags.length > 0 && (
                <div className="space-y-2">
                  <h3 className="font-medium">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {novel.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Synopsis */}
        {novel.synopsis && (
          <Card>
            <CardHeader>
              <CardTitle>Synopsis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose prose-gray dark:prose-invert max-w-none">
                <p className="whitespace-pre-wrap">{novel.synopsis}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Chapters List */}
        {novel.chapters.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Chapters ({novel.chapters.length})</CardTitle>
              <CardDescription>
                Click on any chapter to start reading
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {novel.chapters.map((chapter) => (
                  <Link
                    key={chapter.id}
                    href={`/novels/${novel.id}/chapters/${chapter.id}`}
                    className="block p-4 rounded-lg border hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">
                          Chapter {chapter.order}: {chapter.title}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(chapter.createdAt)}
                        </p>
                      </div>
                      <Play className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Author Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              About the Author
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={novel.author.image || ""} alt={novel.author.name || ""} />
                <AvatarFallback className="text-lg">
                  {novel.author.name?.[0]?.toUpperCase() || "A"}
                </AvatarFallback>
              </Avatar>
              <div className="space-y-2">
                <h3 className="font-semibold text-lg">{novel.author.name}</h3>
                {novel.author.bio ? (
                  <p className="text-muted-foreground">{novel.author.bio}</p>
                ) : (
                  <p className="text-muted-foreground italic">No bio available</p>
                )}
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/authors/${novel.author.id}`}>
                    View Profile
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}