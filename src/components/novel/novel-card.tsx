import Image from "next/image"
import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { LibraryButton } from "@/components/library/library-button"
import { formatDate, truncateText } from "@/lib/utils"
import { BookOpen, User, Calendar } from "lucide-react"
import type { NovelWithAuthor } from "@/store/api/novelsApi"

interface NovelCardProps {
  novel: NovelWithAuthor
  showLibraryButton?: boolean
  showAuthor?: boolean
}

export function NovelCard({
  novel,
  showLibraryButton = true,
  showAuthor = true
}: NovelCardProps) {
  return (
    <Card className="overflow-hidden transition-all duration-200 hover:shadow-lg hover:scale-[1.02] group">
      <CardHeader className="p-0">
        <div className="relative aspect-[3/4] w-full overflow-hidden">
          <Link href={`/novels/${novel.id}`}>
            <Image
              src={novel.coverImage || "/placeholder-cover.svg"}
              alt={novel.title}
              fill
              className="object-cover transition-transform duration-200 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
            />
          </Link>

          {/* Status Badge */}
          <div className="absolute top-2 right-2">
            <Badge
              variant={novel.status === "PUBLISHED" ? "default" : "secondary"}
              className="text-xs"
            >
              {novel.status.toLowerCase()}
            </Badge>
          </div>

          {/* Genre Badge */}
          {novel.genre && (
            <div className="absolute top-2 left-2">
              <Badge variant="outline" className="text-xs bg-background/80 backdrop-blur-sm">
                {novel.genre}
              </Badge>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-4 space-y-3">
        <div className="space-y-2">
          <Link href={`/novels/${novel.id}`}>
            <h3 className="font-semibold text-lg line-clamp-2 hover:text-primary transition-colors">
              {novel.title}
            </h3>
          </Link>

          {showAuthor && (
            <div className="flex items-center gap-2">
              <Avatar className="h-5 w-5">
                <AvatarImage src={novel.author.image || ""} alt={novel.author.name || ""} />
                <AvatarFallback className="text-xs">
                  {novel.author.name?.[0]?.toUpperCase() || "A"}
                </AvatarFallback>
              </Avatar>
              <Link
                href={`/authors/${novel.author.id}`}
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                by {novel.author.name}
              </Link>
            </div>
          )}

          {novel.description && (
            <p className="text-sm text-muted-foreground line-clamp-3">
              {truncateText(novel.description, 120)}
            </p>
          )}
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <BookOpen className="h-3 w-3" />
            <span>{novel._count.chapters} chapters</span>
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>{formatDate(novel.createdAt)}</span>
          </div>
        </div>

        {/* Tags */}
        {novel.tags && novel.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {novel.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {novel.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{novel.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
      </CardContent>

      {showLibraryButton && (
        <CardFooter className="p-4 pt-0">
          <LibraryButton novelId={novel.id} />
        </CardFooter>
      )}
    </Card>
  )
}