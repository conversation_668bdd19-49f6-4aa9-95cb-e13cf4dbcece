# Black Blogs - Novel Writing & Reading Platform

A modern full-stack web application for authors to write and publish novels, and readers to discover and enjoy content.

## 🚀 Technology Stack

- **Framework:** Next.js 14+ with App Router
- **Styling:** Tailwind CSS with shadcn/ui components
- **Database:** Supabase (PostgreSQL)
- **ORM:** Prisma
- **Authentication:** NextAuth.js v4 with Google OAuth
- **State Management:** Redux Toolkit with RTK Query
- **File Storage:** Supabase Storage
- **File Upload:** React Dropzone with drag-and-drop support
- **Deployment:** Vercel (recommended)

## 📋 Features

### ✅ Implemented Features

#### 🔐 Authentication & User Management
- Google OAuth authentication with NextAuth.js v4
- Role-based access control (READER/AUTHOR)
- User profiles and settings
- Session management with database sessions

#### 📚 Novel Management
- Create, edit, and publish novels
- Draft and published status management
- Cover image upload with drag-and-drop interface
- Genre and tag categorization
- Rich text editing for novel descriptions and synopsis

#### 📖 Chapter Management
- Create, edit, and organize chapters
- Drag-and-drop chapter reordering
- Chapter publish/unpublish functionality
- Batch chapter operations
- Rich content editing

#### 🎨 File Upload System
- **Cover Image Upload:** Drag-and-drop interface for novel covers
- **Media Management Dashboard:** Centralized file browser and management
- **Supported Formats:** JPG, PNG, WebP (up to 5MB)
- **Supabase Storage Integration:** Secure cloud storage with public URLs
- **File Validation:** Type and size validation with user feedback

#### 🔍 Advanced Search & Discovery
- **Novel Search API:** Full-text search across titles, descriptions, and authors
- **Advanced Filters:** Genre, author, status, tags, rating filters
- **Author Search:** Find authors by name and bio
- **Genre Browsing:** Dedicated genre pages with novel listings
- **Faceted Search:** Dynamic filter options based on available content

#### 📊 Reading Progress Tracking
- **Progress APIs:** Track reading progress per novel and chapter
- **Reading History:** Personal reading history with statistics
- **Time Tracking:** Monitor time spent reading
- **Progress Visualization:** Progress bars and completion percentages
- **Resume Reading:** Continue from last read position

#### 📈 Analytics Dashboard
- **Novel Performance:** Follower counts, reader engagement, completion rates
- **Chapter Analytics:** Drop-off rates, reading time, completion metrics
- **Performance Insights:** Automated insights about content performance
- **Time-based Filtering:** 7d, 30d, 90d, 1y analytics views
- **Engagement Metrics:** Reader retention and interaction statistics

#### 🎯 Enhanced Novel Features
- **Featured Novels API:** Curated content discovery
- **Table of Contents:** Chapter navigation for novels
- **Novel Status Management:** Draft, Published, Completed, Archived states
- **Author Profiles:** Dedicated author pages with novel listings

#### 🏠 Public Pages
- Homepage with featured content
- Browse page with search and filtering
- Novel detail pages with comprehensive information
- Chapter reading interface with customizable settings
- Author profile pages
- Genre discovery pages

#### 🎨 UI/UX Features
- Responsive design for all screen sizes
- Dark/light theme support
- Loading states and error handling
- Toast notifications for user feedback
- Accessible components with proper ARIA labels
- Modern design with shadcn/ui components

## 🛠️ API Endpoints

### File Upload APIs
- `POST /api/upload/cover` - Upload novel cover images
- `DELETE /api/upload/cover/[filename]` - Delete cover images

### Novel Management APIs
- `GET /api/novels` - List novels with pagination and filtering
- `POST /api/novels` - Create new novel
- `GET /api/novels/[id]` - Get novel details
- `PUT /api/novels/[id]` - Update novel
- `DELETE /api/novels/[id]` - Delete novel
- `GET /api/novels/featured` - Get featured novels

### Chapter Management APIs
- `GET /api/novels/[novelId]/chapters` - List chapters for a novel
- `POST /api/novels/[novelId]/chapters` - Create new chapter
- `GET /api/chapters/[id]` - Get chapter details
- `PUT /api/chapters/[id]` - Update chapter
- `DELETE /api/chapters/[id]` - Delete chapter
- `PATCH /api/chapters/[id]/reorder` - Reorder chapters
- `PATCH /api/chapters/[id]/publish` - Toggle publish status
- `POST /api/chapters/[id]/publish` - Batch publish/unpublish

### Search & Discovery APIs
- `GET /api/search/novels` - Advanced novel search with filters
- `GET /api/search/authors` - Author search functionality

### Reading Progress APIs
- `GET /api/reading-progress` - Get user's reading progress
- `POST /api/reading-progress` - Update reading progress
- `GET /api/reading-progress/[novelId]` - Get progress for specific novel
- `DELETE /api/reading-progress/[novelId]` - Delete reading progress

### Analytics APIs
- `GET /api/analytics/novels` - Novel performance metrics
- `GET /api/analytics/chapters` - Chapter engagement analytics

### Authentication APIs
- `GET/POST /api/auth/[...nextauth]` - NextAuth.js authentication

## 📱 Pages & Routes

### Public Pages
- `/` - Homepage with featured novels
- `/browse` - Novel discovery with search and filters
- `/novels/[id]` - Novel detail page
- `/novels/[id]/chapters` - Table of contents
- `/novels/[id]/chapters/[chapterId]` - Chapter reading page
- `/authors/[id]` - Author profile page
- `/genres` - Genre listing page
- `/genres/[genre]` - Genre-specific novel listings

### User Pages
- `/library` - User's personal library
- `/history` - Reading history and progress
- `/profile` - User profile management
- `/settings` - User settings

### Author Dashboard
- `/dashboard` - Author dashboard overview
- `/dashboard/novels` - Novel management
- `/dashboard/novels/new` - Create new novel
- `/dashboard/novels/[id]` - Novel management page
- `/dashboard/novels/[id]/edit` - Edit novel details
- `/dashboard/novels/[id]/cover` - Upload cover image
- `/dashboard/novels/[id]/chapters/new` - Create new chapter
- `/dashboard/novels/[id]/chapters/[chapterId]/edit` - Edit chapter
- `/dashboard/media` - Media management dashboard
- `/dashboard/analytics` - Analytics dashboard

### Authentication Pages
- `/auth/signin` - Sign in page
- `/auth/error` - Authentication error page
- `/unauthorized` - Unauthorized access page

### Public Features (No Authentication Required)
- 🏠 Homepage with featured/popular novels
- 🔍 Browse page with search and filtering
- 📖 Novel detail pages with synopsis, chapters, author info
- 📚 Chapter reading interface with navigation

### Reader Features (Authenticated Users)
- 📚 Personal library to save favorite novels
- ❤️ Add/remove novels from library
- 📖 Reading progress tracking

### Author Features (AUTHOR Role Required)
- 📊 Author dashboard with novel analytics
- ✍️ Novel creation and editing interface
- 📝 Chapter management (create, edit, reorder, publish/unpublish)
- 🖋️ Rich text editor for writing chapters
- 🖼️ Cover image upload functionality

## 🏗️ Project Structure

```
black-blog/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # Auth-protected routes
│   │   ├── (public)/          # Public routes
│   │   ├── api/               # API routes
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Homepage
│   ├── components/            # Reusable components
│   │   ├── ui/               # shadcn/ui components
│   │   ├── auth/             # Authentication components
│   │   ├── novel/            # Novel-related components
│   │   └── layout/           # Layout components
│   ├── lib/                  # Utility functions
│   │   ├── auth.ts           # NextAuth configuration
│   │   ├── db.ts             # Database connection
│   │   ├── store.ts          # Redux store
│   │   └── utils.ts          # Helper functions
│   ├── store/                # Redux slices and API
│   └── types/                # TypeScript type definitions
├── prisma/                   # Database schema and migrations
├── public/                   # Static assets
└── docs/                     # Project documentation
```

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+ and npm/yarn/pnpm
- Git
- Supabase account
- Google OAuth credentials

### Quick Start Options

#### Option 1: Automated Setup (Recommended)
```bash
# Run the complete setup script
./scripts/quick-start.sh
```

#### Option 2: Supabase CLI Setup
```bash
# Install dependencies
npm install

# Set up Supabase with CLI
./scripts/setup-supabase.sh

# Configure environment variables with Supabase credentials
cp .env.example .env.local
# Update .env.local with credentials from Supabase setup

# Set up database
npx prisma generate
npx prisma db push

# Start development server
npm run dev
```

#### Option 3: Manual Setup
1. **Install dependencies:**
```bash
npm install
```

2. **Environment setup:**
```bash
cp .env.example .env.local
# Fill in your environment variables
```

3. **Database setup:**
```bash
npx prisma generate
npx prisma db push
```

4. **Run development server:**
```bash
npm run dev
```

## 🔧 Environment Variables

Create a `.env.local` file with the following variables:

```env
# Database
DATABASE_URL="your-supabase-database-url"
DIRECT_URL="your-supabase-direct-url"

# NextAuth
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Supabase
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"
```

## 🗄️ Supabase Setup with CLI

For detailed Supabase setup instructions using the CLI, see [docs/SUPABASE_SETUP.md](docs/SUPABASE_SETUP.md).

### Quick Supabase Setup
```bash
# Install Supabase CLI
npm install -g supabase

# Run our automated setup script
./scripts/setup-supabase.sh

# This will:
# - Install Supabase CLI if needed
# - Login to your Supabase account
# - Initialize Supabase in your project
# - Start local Supabase development environment
# - Display connection credentials
```

### Local Development with Supabase
- **Local Database:** PostgreSQL running in Docker
- **Local Studio:** http://localhost:54323
- **Local API:** http://localhost:54321
- **Automatic migrations** and schema synchronization

### Production Deployment
```bash
# Create production project
supabase projects create black-blogs

# Link local to production
supabase link --project-ref your-project-ref

# Deploy schema to production
supabase db push
```

## 📊 Database Schema

The application uses the following main entities:
- **User:** Authentication and profile information
- **Novel:** Book metadata and content
- **Chapter:** Individual chapters within novels
- **Library:** User's saved novels (many-to-many relationship)

## 🚀 Deployment

### Vercel Deployment
1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Database Migration
```bash
npx prisma migrate deploy
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run E2E tests
npm run test:e2e
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.